from fastapi import FastAP<PERSON>, Request
from app.submit_router import submit_router
from app.monitor_router import monitor_router
from app.receive_router import receive_router
from app.vector_search_router import vector_search_router
from app.media_property_retrieval import media_properties_retrieval
from app.ranking_stats import ranking_statistics_router
from app.temporary_analysis import temporary_analysis

import logging
import uvicorn

# Basic logging configuration
logging.basicConfig(
    filename="app.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

app = FastAPI()

app.include_router(submit_router, prefix="/Submit", tags=["Submit"])
app.include_router(monitor_router, prefix="/Monitor", tags=["Monitor"])
app.include_router(receive_router, prefix="/Receive", tags=["Receive"])
app.include_router(media_properties_retrieval, prefix="/MediaPropertiesRetrieval", tags=["Media Properties Retrieval"])
app.include_router(vector_search_router, prefix="/VectorSearch", tags=["VectorSearch"])
app.include_router(ranking_statistics_router, prefix="/RankingStats", tags=["Ranking Stats"])
app.include_router(temporary_analysis, prefix="/TemporaryAnalysis", tags=["Temporary Analysis"])

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, log_level="info")