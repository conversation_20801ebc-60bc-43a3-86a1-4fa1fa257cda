from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from typing import Optional
from .dependencies import *
# from config import *
from .utils import *
from .config import *
import logging
import httpx
import os
import zipfile
from .ip_utils import get_client_ip

DEBUG_MODE = os.getenv('DEBUG_MODE', False)
logging.basicConfig(
    datefmt='%m/%d/%y | %I:%M:%S %p',
    format='%(asctime)s| %(threadName)s| %(levelname)s| %(lineno)3s| %(filename)s: %(message)s',
    handlers=[logging.StreamHandler()]
)
logging.info('logger initialized')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)

submit_router = APIRouter()

class InputRequestPayload(BaseModel):
    file_url: Optional[str] = None
    fileId: str
    env: Optional[str] = "Default"
    # add entity_id as optional
    entity_id: Optional[str] = "Default"
    event_id: Optional[str] = "Default"
    crawler: Optional[str] = "Default"
    is_profile_image: Optional[bool] = False
    redaction: Optional[bool] = False
    priority: Optional[int] = 50
    mediaType: Optional[str] = "Default"
    metaData: Optional[Dict[str, Any]] = None



@submit_router.post("/submit_request", tags=["Submit"])
async def submit_request(payload: InputRequestPayload, req: Request):
    logger.info("-----##########-----")
    logger.info(payload)
    logger.info(f"IP INFO: {get_client_ip(req)}")
    logger.info("-----##########-----")

    if not payload.file_url:
        logger.info(f"File URL not provided. Generating a downloadable url for the file id: {payload.fileId}")
        payload.file_url = await get_downloadable_url_from_id_v2(payload.fileId, v2=True)
        logger.info(f"Downloadable URL : {payload.file_url}")

    if payload and payload.file_url:
        payload.file_url = await make_url_public(payload.fileId, payload.file_url)
        logger.info(f"Public URL : {payload.file_url}")


    if payload.mediaType != "Default" and payload.mediaType != "" and payload.mediaType != None and payload.mediaType != "string":
        pass
    else:
        #fetch the media type from the docserver
        try:
            file_info = await get_file_info(payload.fileId,"fileType,_entityIds,_eventIds,userId,fileName")
            fileType = file_info["fileType"]
            fileName: str = file_info.get("fileName", "").lower()

            logger.info(f"FileName: {fileName} - FileType: {fileType}")
            
            if "image" in fileType.lower():
                payload.mediaType = "Image"
                logger.info(f"Media type is Image")
            elif "video" in fileType.lower():
                payload.mediaType = "Video"
            elif "audio" in fileType.lower():
                payload.mediaType = "Audio"
            elif "text" in fileType.lower():
                payload.mediaType = "Text"
            elif "other" in fileType.lower() and fileName and (fileName.endswith(".3gp") or fileName.endswith(".mkv")):
                payload.mediaType = "Video"

            if "userId" in file_info and file_info["userId"]:
                logger.info(f"User triggered action - Setting priority to 300")
                payload.priority = 300
        except Exception as e:
            logger.error(e)
            raise HTTPException(status_code=500, detail=f"Failed to fetch the media type from the docserver for the file {payload.fileId}")


    env = payload.env
    if env != "Default" and env != "" and env != None and env != "string" and env != "akscluster-stg-2023-m4rch-1234" and env != "cybersmart-dev-akscluster2":
        payload.priority = payload.priority * 2

    # Auto move to backblaze if on_prem
    if IS_ON_PREM:
        payload.file_url = auto_move_file_to_temp(payload.file_url)


    # if it is image, insert the clip embedding into vector database

    if PROCESS_CLIP_EMBEDDINGS: 
        if payload.mediaType == "Image":
            try:
                if 'redaction' in payload and not payload.redaction:
                    await process_clip_embeddings(payload, file_info, get_crawler_from_file_id, CLIP_EMBEDDING_INSERT_URL, save_clip_embeddings_docserver, update_file, logger)
            except Exception as e:
                logger.info(f"Failed to insert clip embedding for file_id: {payload.fileId}")


            # this is to process gdino clip embeddings
            gdino_flag = {
                "_fileProcessorStatus":{
                    "gdino_object_detection": "NotYetProcessed"
                }
            }
            await update_file(payload.fileId, gdino_flag)
    
    if payload.mediaType == "Video":
        # update the thumbnail for the video
        thumbnail_res = await update_thumbnail(payload.fileId, payload.file_url)

    aiGatewatRequestBody = {
        "file_url": payload.file_url,
        "fileId": payload.fileId,
        "entity_id": payload.entity_id,
        "event_id": payload.event_id,
        "crawler": payload.crawler,
        "is_profile_image": payload.is_profile_image,
        "redaction": payload.redaction,
        "metaData": payload.metaData
    }
    # use https
    try:
        response = await add_task_to_ai_gateway_queue(aiGatewatRequestBody, priority=payload.priority, env = payload.env, mediaType=payload.mediaType)
        logger.info(f"Added task to the AI Gateway queue with the response: {response}")
    except Exception as e:
        logger.error(e)
        raise HTTPException(status_code=500, detail="Failed to submit request to the AI Gateway service")

    return {"status": "success", "message": "Request submitted successfully", "payload": payload}

@submit_router.post("/submit_zip", tags=["Submit"])
async def submit_zip(payload: InputRequestPayload, req: Request):
    logger.info("-----##########-----")
    logger.info(payload)
    logger.info(f"IP INFO: {get_client_ip(req)}")
    logger.info("-----##########-----")
    if payload.env != "Default" and payload.env != "" and payload.env != None and payload.env != "string":
        pass
    else:
        payload.env = DEFAULT_SYSTEM_ID
    # Steps:
    # 1. Download the zip file
    # 2. Unzip the file
    # 3. Upload the files to the file server
    # 4. Update the file in file collection with the ids of the uploaded files
    # 5. Submit the request to the AI Gateway service for each file
        
    if payload.priority != "Default" and payload.priority != "" and payload.priority != None and payload.priority != "string":
        pass
    else:
        payload.priority = 100

    priority = payload.priority
    #Download the zip file from the file url with httpx
    zip_path = f"{payload.fileId}.zip"
    folder_path = f"{payload.fileId}"
    await download_file(payload.file_url, zip_path)
    # Unzip the file
    try:
        extract_to = folder_path
        logger.info(f"Extracting all files to {extract_to}")
        if not os.path.exists(extract_to):
            os.makedirs(extract_to)
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
            logger.info(f"Extracted all files to {extract_to}")
    except Exception as e:
        logger.error(e)
        raise HTTPException(status_code=500, detail="Failed to extract the zip file")

    entityIds = ""
    caseIds = ""
    # Get the entity id or event id
    if payload.entity_id != "Default" and payload.entity_id != "" and payload.entity_id != None and payload.entity_id != "string":
        entityIds = payload.entity_id
    if payload.event_id != "Default" and payload.event_id != "" and payload.event_id != None and payload.event_id != "string":
        caseIds = payload.event_id
    # Upload the files to the file server
    uploaded_file_list = []
    # try:
    #     files = os.listdir("temp")
    #     for file in files:
    #         # construct the full path of the file not the relative path
    #         cwd = os.getcwd()
    #         full_path = os.path.join(cwd, "temp", file)
    #         res = await upload_file(full_path, caseIds, entityIds, payload.fileId)
    #         _id = res["_id"]
    #         fileUrl = res["fileUrl"]
    #         logger.info(f"File {file} uploaded successfully with file id {_id}")
    #         uploaded_file_list.append({"fileId": _id, "fileUrl": fileUrl})

    # except httpx.HTTPError as e:
    #     logger.error(e)
    #     raise HTTPException(status_code=500, detail="Failed to upload the files to the file server")

    try:
        for root, dirs, files in sorted(os.walk(extract_to, topdown=True)):
            for file in sorted(files):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, extract_to)
                file_name = file
                # upload the file to the file server
                res = await upload_file(file_path, caseIds, entityIds, payload.fileId, file_name)
                _id = res["_id"]
                fileUrl = res["fileUrl"]
                logger.info(f"File {file} uploaded successfully with file id {_id}")
                uploaded_file_list.append({"fileId": _id, "fileUrl": fileUrl, "rel_path": rel_path})
    except Exception as e:
        logger.error(e)



    # Update the file in file collection with the ids of the uploaded files
    logger.info(f"uploaded_file_list: {uploaded_file_list}")
    update_document = {
        "file_structure": uploaded_file_list
    }
    try:
        response = await update_file(payload.fileId, update_document)
        logger.info(response)
    except Exception as e:
        logger.error(e)
        raise HTTPException(status_code=500, detail="Failed to update the file in the file collection")
    
    # Submit the request to the AI Gateway service for each file
    for file in uploaded_file_list:
        file_url = await make_url_public(file["fileId"], file["fileUrl"])
        # Auto move to backblaze if on_prem
        if IS_ON_PREM:
            file_url = auto_move_file_to_temp(file_url)

        # Send the request using the above submit_request endpoint
        # payload = InputRequestPayload(
        #     file_url = file_url,
        #     fileId = file["fileId"],
        #     env = DEFAULT_SYSTEM_ID,
        #     entity_id = entityIds,
        #     event_id = caseIds,
        #     is_profile_image = False,
        #     priority=payload.priority
        # )
        payload = {
            "file_url": file_url,
            "fileId": file["fileId"],
            "env": DEFAULT_SYSTEM_ID,
            "entity_id": entityIds,
            "event_id": caseIds,
            "is_profile_image": False,
            "priority": priority,
            "mediaType": payload.mediaType,
            "metaData": payload.metaData
        }
        logger.info(f"Sending the request to the submit_request endpoint: {payload}")
        # call the local host submit_request endpoint
        # endpoint = "http://localhost:8000/Submit/submit_request"
        input_payload = InputRequestPayload(**payload)
        try:
            # async with httpx.AsyncClient() as client:
            #     response = await client.post(endpoint, json=payload, timeout=30)
            #     if response.status_code == 200:
            #         logger.info(f"Request submitted successfully for file {file['fileId']} with the response: {response.json()}")
            #     else:
            #         logger.error(f"Failed to submit the request for file {file['fileId']} with the response: {response.json()}")
            response = await submit_request(input_payload)
        except Exception as e:
            logger.error(e)
            raise HTTPException(status_code=500, detail="Failed to submit the request to the AI Gateway service")                    
    # Remove the temp folder and the zip file
    # delete the temp folder
    os.system(f"rm -rf {extract_to}")
    # delete the zip file
    os.system(f"rm -rf {zip_path}")
    return {"status": "success", "message": "Request submitted successfully"}