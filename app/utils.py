import json
import logging
import mimetypes
import os
import tempfile
from datetime import <PERSON><PERSON><PERSON>
from io import Bytes<PERSON>
from ipaddress import ip_address
from socket import gaierror, gethostbyname
from typing import Optional
from urllib.parse import quote, urlparse

import cv2
import httpx
import numpy as np
import requests
from azure.cognitiveservices.vision.computervision import ComputerVision<PERSON><PERSON>
from azure.cognitiveservices.vision.computervision.models import (
    Details,
    VisualFeatureTypes,
)
from azure.cognitiveservices.vision.face import FaceClient
from minio import Minio
from msrest.authentication import CognitiveServicesCredentials
from PIL import Image
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_fixed, wait_exponential, retry_if_result, retry_any, before_sleep, before_sleep_log
from .config import *

DEBUG_MODE = os.getenv('DEBUG_MODE', False)
logging.basicConfig(
    datefmt='%m/%d/%y | %I:%M:%S %p',
    format='%(asctime)s| %(threadName)s| %(levelname)s| %(lineno)3s| %(filename)s: %(message)s',
    handlers=[logging.StreamHandler()]
)
logging.info('logger initialized')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)


social_networks_profle_image_mapping = {
    "facebookData": "facebookProfileImgUrlGlobal",
    "instagramData": "profileImage",
    "twitterData": "profileImage",
    "linkedinData": "profileImage",
    "youtubeData": "profileImage",  
}

dns_cache = {}

class DownloadFileTemp(object):
    _temp_file: Optional[tempfile.NamedTemporaryFile] = None
    _url: str
    _prefix: Optional[str] = None

    def __init__(self, url, prefix=None):
        self._url = url
        self._prefix = prefix

    def __enter__(self):
        # Send a GET request to the URL
        response = requests.get(self._url, stream=True)
        response.raise_for_status()  # Raise an error for bad status codes

        # Create a temporary file
        self._temp_file = tempfile.NamedTemporaryFile(delete=False, prefix=self._prefix)

        # Write the content to the temporary file
        with open(self._temp_file.name, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:  # Filter out keep-alive new chunks
                    f.write(chunk)

        return self

    @property
    def name(self):
        return self._temp_file.name

    def __exit__(self, *args):
        if self._temp_file is not None:
            os.remove(self.name)


# Retry configuration for handling network and recoverable exceptions
@retry(
    stop=stop_after_attempt(NUM_RETRIES),
    wait=wait_fixed(WAIT_BETWEEN_RETRIES),
    retry=retry_if_exception_type((gaierror, ConnectionError, TimeoutError))  # Retry on specific exceptions
)

def move_file_to_backblaze(downloaded_file: DownloadFileTemp, object_name) -> str:
    backblaze_client = Minio(
        BACKBLAZE_S3_URL,
        BACKBLAZE_ACCOUNT_ID,
        BACKBLAZE_ACCOUNT_SECRET,
        secure=True
    )

    backblaze_client.fput_object(BACKBLAZE_BUCKET_NAME, object_name, downloaded_file.name)
    file_url = backblaze_client.get_presigned_url(
        "GET", BACKBLAZE_BUCKET_NAME, object_name,
        timedelta(seconds=BACKBLAZE_PRESIGNED_GET_URL_EXPIRY)
    )
    return file_url

def get_sas_blob_url(file_name: str, container="files", ttl=1800) -> Optional[str]:
    h = {
        "Ocp-Apim-Subscription-Key": OCP_APIM_SUBSCRIPTION_KEY
    }
    with httpx.Client() as client:
        r = client.get(
            url=os.path.join(DOSASHOP_API_URL, 'azblob/getbloburi', container, file_name, str(ttl)),
            headers=h,
            timeout=None,
        )

        if r.status_code == 404:
            return None
        r.raise_for_status()
        return r.text


def move_file_to_sas(downloaded_file: DownloadFileTemp, object_name) -> str:
    h = {
        "Ocp-Apim-Subscription-Key": OCP_APIM_SUBSCRIPTION_KEY
    }
    with open(downloaded_file.name, 'rb') as f, httpx.Client() as client:
        r = client.put(
            url=os.path.join(DOSASHOP_API_URL, 'azblob', "files", object_name),
            headers=h,
            timeout=None,
            content=f.read()
        )
        r.raise_for_status()
        file_url = get_sas_blob_url(object_name, "files")
        if file_url is None:
            raise RuntimeError(f"failed to move file to SAS. filename: {object_name}")

    return file_url

def auto_move_file_to_temp(file_url: str, force_move=False) -> Optional[str]:
    if file_url is None:
        return None

    try:
        if file_url.startswith("/"):
            file_url = f"{FILE_SERVER_DOWNLOAD_URL}{file_url}"

        parsed_url = urlparse(file_url)
        hostname = parsed_url.hostname

        # Retrieve IP address from dns_cache or perform DNS lookup if not cached
        url_ip = dns_cache.get(hostname)
        if url_ip is None:
            # Perform direct DNS lookup and store it in dns_cache
            url_ip = ip_address(gethostbyname(hostname))
            dns_cache[hostname] = url_ip  # Cache the result

        # Check if we need to move the file based on IP privacy
        if not force_move:
            if not url_ip.is_private:
                return file_url

        # Proceed with file upload to Backblaze
        filename = os.path.basename(parsed_url.path)
        source = parsed_url.hostname.replace(".", "__")
        object_name = os.path.join(source, filename)


        with DownloadFileTemp(file_url, prefix=source + "__") as temp_file:
            if USE_SAS_TEMP:
                new_file_url = move_file_to_sas(temp_file, object_name)
            else:
                new_file_url = move_file_to_backblaze(temp_file, object_name)
    except Exception as e:
        logger.error(f"Exception while moving file to temp: {e}")
        raise e

    return new_file_url



async def update_entity_card(entity_card_id, update_data):
    url = f"{DOC_SERVER_BASE_URL}/docs/EntityCard/{entity_card_id}"
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.patch(url, headers=headers, json=update_data)
    # response = requests.patch(url, headers=headers, json=update_data)
    if response.status_code == 200:
        return response.json()
    else:
        return response.status_code, response.text


async def get_processed_image_file_count():
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = {
        "query": 'fileType:"image" AND _exists_: _fileProcessorStatus.FacialRecognition AND _fileProcessorStatus.FacialRecognition : "processed"'
    }
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=query, headers=headers)
    # response = requests.get(url, params=query, headers=headers)
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}

async def get_unprocessed_image_file_count():
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = {
        "query": 'fileType:"image" AND _exists_: _fileProcessorStatus.FacialRecognition AND NOT _fileProcessorStatus.FacialRecognition : "processed"'
    }
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params=query, headers=headers)
    # response = requests.get(url, params=query, headers=headers)
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}
    
async def get_processed_entity_image_count(entity_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_entityIds : \"{entity_id}\" AND _exists_: _fileProcessorStatus.FacialRecognition AND fileType:\"image\" AND _fileProcessorStatus.FacialRecognition : \"processed\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        logger.info(f"Response from docserver for get_processed_entity_image_count for the entity id {entity_id}: {response.json()}")
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}
    
async def get_unprocessed_entity_image_count(entity_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_entityIds : \"{entity_id}\" AND fileType:\"image\" AND _exists_: _fileProcessorStatus.FacialRecognition AND NOT _fileProcessorStatus.FacialRecognition : \"processed\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}

async def get_processed_case_image_count(event_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_eventIds : \"{event_id}\" AND fileType:\"image\" AND _exists_: _fileProcessorStatus.FacialRecognition AND _fileProcessorStatus.FacialRecognition : \"processed\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}
    
async def get_unprocessed_case_image_count(event_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_eventIds : \"{event_id}\" AND fileType:\"image\" AND _exists_: _fileProcessorStatus.FacialRecognition AND NOT _fileProcessorStatus.FacialRecognition : \"processed\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}
    
async def get_total_entity_image_count(entity_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_entityIds : \"{entity_id}\" AND fileType:\"image\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}

async def get_total_case_image_count(event_id):
    url = f"{DOC_SERVER_BASE_URL}/files/_count"
    query = f"_eventIds : \"{event_id}\" AND fileType:\"image\""
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, params={'query': query}, headers=headers)
    # response = requests.get(url, headers=headers, params={'query': query})
    if response.status_code == 200:
        return response.json()["count"]
    else:
        return {'error': f'Failed with status code {response.status_code}'}
    
async def update_file(file_id, payload):
    url = f"{DOC_SERVER_BASE_URL}/files/{file_id}"

    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }

    async with httpx.AsyncClient() as client:
        response = await client.patch(url, headers=headers, data=json.dumps(payload), timeout=TIMEOUT_IN_SEC)
    # response = requests.patch(url, headers=headers, data=json.dumps(payload), timeout=TIMEOUT_IN_SEC)

    # Check the response
    if response.status_code == 200:
        return response.json()
    else:
        return {"Error": response.status_code, "Message": response.text}

async def update_document(doc_id: str, collection: str, data: dict):
    logging.info(f"Updating document {doc_id} in collection {collection} with data {data}")
    try:
        url = f"{DOC_SERVER_BASE_URL}/docs/{collection}/{doc_id}"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        async with httpx.AsyncClient() as client:
            response = await client.patch(url, headers=headers, data=json.dumps(data))
        # response = requests.patch(url, headers=headers, data=json.dumps(data))

        if response.status_code != 200:
            raise Exception(f"HTTP error! status: {response.status_code}, response: {response.text}")

        return response.json()
    except Exception as e:
        logger.error(f"Failed to update document {doc_id} with exception {e}")
        
async def locate_file(file_id: str):
    try:
        url = f"{DOCSERVER_V2_LOCATE_FILE}{file_id}?fieldsToReturn=_id"
        # url = f"{DOC_SERVER_BASE_URL}/files/{file_id}?fieldsToReturn=_id"
        headers = {
            "accept": "application/json"
        }
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
        # response = requests.get(url, headers=headers)

        if response.status_code != 200:
            raise Exception(f"HTTP error! status: {response.status_code}, response: {response.text}")

        return response.json()
    except Exception as e:
        logger.error(f"Failed to locate file {file_id} with exception {e}")

def retry_condition(response):
    return not response or response.status_code != 200

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
    retry=retry_any(
        retry_if_result(retry_condition),
        retry_if_exception_type(Exception)
    ),
    before_sleep=before_sleep_log(logger, logging.WARNING)
)
async def _send_task_request(aiGateWayRequestBody, headers, url):
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=aiGateWayRequestBody, timeout=TIMEOUT_IN_SEC)

            if not USE_INTERNAL_AI_GATEWAY:
                await client.post(AI_GATEWAY_TASK_CREATION_URL, json=aiGateWayRequestBody, timeout=TIMEOUT_IN_SEC)

            return response
    except Exception as e:
        logger.warning(f"Retrying due to exception: {e}")
        return None  # triggers retry

async def add_task_to_ai_gateway_queue(aiGatewatRequestBody, priority=50, env="cybersmart-dev-akscluster2", mediaType="Image"):
    aiGateWayRequestBody = {
        'apiBody': json.dumps(aiGatewatRequestBody),
        'priority': str(priority),
        'env': env,
        'queryVars': [],
        'mediaType': mediaType
    }

    file_id = aiGatewatRequestBody.get("fileId")
    url = AI_GATEWAY_TASK_CREATION_URL if USE_INTERNAL_AI_GATEWAY else PROXY_AI_GATEWAY_TASK_CREATION_URL
    headers = (
        {'Content-Type': 'application/json'}
        if USE_INTERNAL_AI_GATEWAY else
        {'Ocp-Apim-Subscription-Key': OCP_APIM_SUBSCRIPTION_KEY, 'Cache-Control': CACHE_CONTROL}
    )

    logger.info(f"Payload to the AI gateway: {aiGateWayRequestBody}")
    try:
        response = await _send_task_request(aiGateWayRequestBody, headers, url)

        if response and response.status_code == 200:
            logger.info(f"Response from the AI gateway: {response.text}")
            status_update_payload = {"_fileProcessorStatus": {"SenttoAIGateway": "true"}}
            await update_file(file_id, status_update_payload)
            return response.json()
        else:
            raise Exception(f"Final attempt failed. Response: {response.text if response else 'No response'}")

    except Exception as e:
        logger.error(f"Failed to add task to AI Gateway after retries. Error: {e}")
        status_update_payload = {
            "_fileProcessorStatus": {
                "SenttoAIGateway": "false",
                "SenttoAIGatewayError": str(e)
            }
        }
        await update_file(file_id, status_update_payload)


async def upload_to_docserver_v2(file):
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=f"{DOC_SERVER_V2_URL}/files", files=file
            , timeout=30)
            url = response.json()["fileUrl"]
            _id = response.json()["_id"]
        return _id, url
    except Exception as e:
        #logger.error(e)
        raise httpx.HTTPError("error sending image binary")
    
async def upload_file(file_path, case_ids, entity_ids, parentDocId, file_name):
    try:
        # Guess the MIME type of the file
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'  # Default to binary data
        # Read the file in binary mode
        with open(file_path, 'rb') as f:
            files = {
                'file': (file_name, f, mime_type),
                'caseIds': (None, case_ids),
                'entityIds': (None, entity_ids),
                'parentDocId': (None, parentDocId)
                }
            # Perform the asynchronous POST request
            async with httpx.AsyncClient() as client:
                response = await client.post(url=f"{DOC_SERVER_V1_URL}files/", files=files, timeout=30)
                return response.json()  # Assuming the server responds with JSON
    except Exception as e:
        #logger.error(e)
        raise httpx.HTTPError("error sending image binary")
    
async def upload_file(file_path,file_name):
    try:
        # Guess the MIME type of the file
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'  # Default to binary data
        # Read the file in binary mode
        with open(file_path, 'rb') as f:
            files = {
                'file': (file_name, f, mime_type)
                }
            # Perform the asynchronous POST request
            async with httpx.AsyncClient() as client:
                response = await client.post(url=f"{DOC_SERVER_V1_URL}files/", files=files, timeout=30)
                return response.json()  # Assuming the server responds with JSON
    except Exception as e:
        #logger.error(e)
        raise httpx.HTTPError("error sending image binary")
        
async def download_file(url, save_path):
    headers = {
        "CF-Access-Client-Id": "084161eb89f7b63ed356bdb11cc415f3.access",
        "CF-Access-Client-Secret": "918ae96432f5ba9e69547900ea36a7618595ce83393cd4aa6cd5308edae76dbe"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
        response.raise_for_status()
    # response = requests.get(url, headers=headers)
    # response.raise_for_status()  # To ensure a valid response, raise an exception on failure

    with open(save_path, 'wb') as f:
        f.write(response.content)

# def upload_file(file_path):
#     # Guess the MIME type of the file
#     mime_type, _ = mimetypes.guess_type(file_path)
#     if mime_type is None:
#         mime_type = 'application/octet-stream'  # Default to binary data
#     logger.info(f"full path: {file_path}")
#     # Open the file in binary mode
#     with open(file_path, 'rb') as f:
#         files = {'file': (file_path, f, mime_type)}
#         response = requests.post(url=f"{DOC_SERVER_V1_URL}files/", files=files)
#         logger.info(response)
#         logger.info(response.json())
#     return response.json()

async def get_downloadable_url_from_id_v2(file_id: str, v2: bool = False) -> str:
    base_url = DOC_SERVER_V2_BASE_URL if v2 else DOC_SERVER_BASE_URL
    url = f"{base_url}/files/{file_id}?download=true&return_url=true"
    async with httpx.AsyncClient() as client:
        response = await client.get(url, timeout=60)
    
    if not response.status_code == 200:
        return {"error": f"Failed to generate a downloadable URL for the file id {file_id}"}

    url = response.json()["fileUrl"]
    
    return url

async def get_file(file_id):
    """
    Retrieves the file information for the given file_id.

    Args:
    file_id (str): The ID of the file to retrieve.

    Returns:
    dict: The response data from the server.
    """
    url = f"{DOC_SERVER_BASE_URL}/files/{file_id}"
    headers = {'accept': 'application/json'}
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
    # response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()  # Returns the JSON response if successful
    else:
        return {'error': 'Request failed', 'status_code': response.status_code}
    
async def get_file_info(doc_id, fields_to_return=""):
    try:
        """
        Fetch file information from the docserver.

        Parameters:
        doc_id (str): Document ID to fetch information for.
        fields_to_return (str): Comma-separated fields to return in the response.
        """
        url = f"{DOC_SERVER_BASE_URL}/files/{doc_id}?fieldsToReturn={fields_to_return}"
        if fields_to_return=="":
            url = f"{DOC_SERVER_BASE_URL}/files/{doc_id}"
        headers = {"accept": "application/json"}
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, timeout=TIMEOUT_IN_SEC)
        # response = requests.get(url, headers=headers, timeout=TIMEOUT_IN_SEC)
        
        if response.status_code == 200:
            return response.json()
        else:
            print("Error:", response.status_code, response.text)
    except Exception as e:
        logger.error(f"Failed to fetch file info for the doc id {doc_id} with exception {e}")
        return None
    
async def get_entity_card(entity_id):
    """
    Retrieves the entity card for the given entity_id.

    Args:
    entity_id (str): The ID of the entity to retrieve.

    Returns:
    dict: The response data from the server.
    """
    url = f"{DOC_SERVER_BASE_URL}/docs/EntityCard/{entity_id}"
    headers = {'accept': 'application/json'}
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, timeout=TIMEOUT_IN_SEC)
    # response = requests.get(url, headers=headers, timeout=TIMEOUT_IN_SEC)

    if response.status_code == 200:
        return response.json()  # Returns the JSON response if successful
    else:
        return None

def convert_similar_images_by_face_to_rels(response):
    converted = []

    # Iterate through each face_id in the search_results
    for face_id, face_data in response["search_results"].items():
        face_url = face_data.get("face_url", "")
        similar_images = face_data.get("similar_images", [])
        most_common_entity_id = face_data.get("most_common_entity_id", {})
        entity_name = most_common_entity_id.get("entity_name", "")
        # Process each similar image
        for counter, image in enumerate(similar_images, start=1):
            relationship = {
                "rel": {
                    "relationshipType": "IMAGE",
                    "relationshipDate": "",  # Assuming no date is provided
                    "confidence": round(image.get("distance", 0), 2),
                },
                "from": {
                    "textFullName": entity_name,
                    "_id": face_id,
                    "nodeId": f"FILE__{face_id}",
                    "entityType": "PERSONS"
                },
                "to": {
                    "textFullName": image.get("img_id", ""),
                    "_id": image.get("img_id", ""),
                    "nodeId": f"IMAGE{counter}_{entity_name}",
                    "entityType": "IMAGE",
                }
            }
            converted.append(relationship)
    
    return converted

async def upload_file_using_url(file_source_url, parent_doc_id):
    url = f"{DOC_SERVER_BASE_URL}/files/"
    headers = {
        "accept": "application/json",
    }
    if not file_source_url.startswith(('http://', 'https://')):
        file_source_url = 'http://' + file_source_url
    files = {
        'fileSourceUrl': (None, file_source_url),
        'parentDocId': (None, parent_doc_id)
    }
    
    logger.info(f"Uploading file with source url {file_source_url} and parent doc id {parent_doc_id}")
    async with httpx.AsyncClient() as client:
        response = await client.post(url, files=files, headers=headers)
    # response = requests.post(url, files=files, headers=headers)
    if response.status_code == 200:
        #logger.info(f"File uploaded successfully with response {response.json()} for the file source url {file_source_url}")
        return response
    else:
        logger.error(f"Failed to upload file with status code {response.status_code} and response {response.text} for the file source url {file_source_url}")

async def request_file(file_id):
    try:
        # Base URL for the request
        base_url = f"{DOC_SERVER_V2_BASE_URL}/requestFile/"
        
        # Full URL with the provided file_id
        url = f"{base_url}{file_id}?redirect=false"
        
        # Headers for the request
        headers = {
            'accept': 'application/json'
        }
        
        # Perform the GET request
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
        # response = requests.get(url, headers=headers)
        
        # Check for successful request
        if response.status_code == 200:
            return response.json()  # Assuming the response is in JSON format
        else:
            # Handle errors appropriately
            return {"error": f"Failed to retrieve file. Status code: {response.status_code}"}
    except Exception as e:
        return None
    
async def make_url_public(file_id,file_url):
    if file_url.startswith("http"):
        return file_url
    elif file_url.startswith("docserver2/"):
        # call the request file endpoint to get the file url
        request_response = await request_file(file_id)
        if request_response and "urls" in request_response and len(request_response["urls"]) > 0:
            return request_response["urls"][0]
    else:
        if 'dev' in DEFAULT_SYSTEM_ID or 'DEV' in DEFAULT_SYSTEM_ID:
            return f"{DEV_PUBLIC_HEADER}{file_url}"
        elif 'stg' in DEFAULT_SYSTEM_ID or 'STG' in DEFAULT_SYSTEM_ID:
            return f"{STG_PUBLIC_HEADER}{file_url}"
        elif 'providence' in DEFAULT_SYSTEM_ID or 'PROVIDENCE' in DEFAULT_SYSTEM_ID:
            return f"{PROVIDENCE_PUBLIC_HEADER}{file_url}"
        else:
            if IS_ON_PREM:
                return f"{FILE_SERVER_BASE_URL}{file_url}"
            if USE_INTERNAL_AI_GATEWAY:
                return f"{FILE_SERVER_BASE_URL}{file_url}"
        return f"{CCE_PUBLIC_HEADER}{file_url}"
        
def convert_numpy_types(data):
    if isinstance(data, dict):
        return {k: convert_numpy_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(v) for v in data]
    elif isinstance(data, np.ndarray):
        # Convert array to a list of lists or list of tuples
        return data.tolist()
    elif isinstance(data, (np.float32, np.float64)):
        return float(data)
    elif isinstance(data, (np.int32, np.int64)):
        return int(data)
    else:
        return data
    
async def fetch_unbackuped_files(backup_id,itemsPerPage):
    url = f"{DOC_SERVER_BASE_URL}/files/"
    params = {
        "query": f'NOT BackupId : {backup_id} AND _fileProcessorStatus.FacialRecognition: "processed" AND _exists_: persons.face_embedding',
        "fieldsToReturn": "persons,fileUrl,fileType",
        "itemsPerPage": itemsPerPage
    }
    headers = {
        "accept": "application/json"
    }
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params, headers=headers)
        # response = requests.get(url, params=params, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to fetch unbackuped files with status code {response.status_code} and response {response.text}")
    except Exception as e:
        logger.error(f"Failed to fetch unbackuped files with exception {e}")


async def create_entity_card(data):
    """
    Sends a POST request to create a new entity card.

    Args:
    data (dict): The data to be sent in the request body.

    Returns:
    dict: The response data from the server.
    """
    url = f"{DOC_SERVER_BASE_URL}/docs/EntityCard"
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=data, timeout=TIMEOUT_IN_SEC)
    # response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT_IN_SEC)

    if response.status_code == 200:
        return response.json()  # Returns the JSON response if successful
    else:
        return {'error': 'Request failed', 'status_code': response.status_code}
    
def get_entity_or_case(entity_id, event_id):

    if type(entity_id) == str:
        if entity_id != "" and entity_id != "None":
            return "Entity", entity_id
    if type(entity_id) == list:
        if len(entity_id) > 0 and entity_id[0] != "" and entity_id[0] != "None":
            return "Entity", entity_id[0]
    if type(event_id) == str:
        if event_id != "" and event_id != "None":
            return "Case", event_id
    if type(event_id) == list:
        if len(event_id) > 0 and event_id[0] != "" and event_id[0] != "None":
            return "Case", event_id[0]
        
    
async def create_relationship(data):
    """
    Creates a relationship using the provided data payload.

    Args:
        data (dict): The complete data payload for the relationship.

    Returns:
        dict: The response data from the server, or an error message.
    """
    url = f"{DOC_SERVER_BASE_URL}/relationships/"
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=data, timeout=TIMEOUT_IN_SEC)
    # response = requests.post(url, headers=headers, json=data, timeout=TIMEOUT_IN_SEC)

    if response.status_code == 200:
        return response.json()  # Returns the JSON response if successful
    else:
        try:
            error_detail = response.json()
        except ValueError:
            error_detail = response.text  # Response is not JSON, use raw text

        return {
            'error': 'Request failed',
            'status_code': response.status_code,
            'detail': error_detail
        }

    
async def get_case_document(case_id):
    url = f"{DOC_SERVER_BASE_URL}/docs/Cases/{case_id}"
    headers = {"accept": "application/json"}
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
    # response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()  # Returns the JSON response if successful
    else:
        return response.status_code 
    

async def get_info_from_google_lens(public_url):
    proxies = {
                "http": "http://brd-customer-hl_3d7ceccc-zone-gsearch-country-sg:<EMAIL>:22225",
                "https": "http://brd-customer-hl_3d7ceccc-zone-gsearch-country-sg:<EMAIL>:22225"
            }
    # proxies = {
    #                 "http://": "http://brd-customer-hl_3d7ceccc-zone-gsearch-country-sg:<EMAIL>:22225",
    #                 "https://": "http://brd-customer-hl_3d7ceccc-zone-gsearch-country-sg:<EMAIL>:22225"
    #             }
    encoded_url = quote(public_url, safe='')
    target_url = f"https://lens.google.com/uploadbyurl?url={encoded_url}&brd_json=1"
    try:
        # async with httpx.AsyncClient(proxies=proxies, verify=False) as client:
        #     response = await client.get(target_url, timeout=TIMEOUT_IN_SEC)
        response = requests.get(target_url, proxies=proxies, verify=False, timeout=TIMEOUT_IN_SEC)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to fetch info from google lens for the url: {public_url} with status code {response.status_code} and response {response.text}")
            return None
    except Exception as e:
        logger.error(f"Failed to fetch info from google lens for the public url {public_url} with exception {e}")
        return None

async def get_images_with_people_from_entity(entity_id, fields_to_return):
    try:
        url = f"{DOC_SERVER_BASE_URL}/files/"
        params = {
            "query": f'_entityIds: "{entity_id}" AND fileType: "image" AND _exists_: "persons.face_id"',
            "fieldsToReturn": fields_to_return,
            "sortByField": "-_modifiedDate"
        }
        headers = {
            "accept": "application/json"
        }
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params, headers=headers)
        # response = requests.get(url, params=params, headers=headers)
        return response.json()
    except Exception as e:
        logger.error(f"Failed to fetch images with people from entity {entity_id} with exception {e}")

def find_common_img_id(data):
    common_image_set = []
    image_list = []
    count =1
    for item in data.values():
        imgs = item["imgs"]
        img_ids = [img["img_id"] for img in imgs]
        image_list.append(set(img_ids))



    common_image_set = set.intersection(*image_list)
    return common_image_set

cv_client = ComputerVisionClient(endpoint="https://" + AZURE_REGION + ".api.cognitive.microsoft.com/",credentials=CognitiveServicesCredentials(AZURE_API_SUBSCRIPTION_KEY))

async def url_to_stream(url, save_as=''):
    #   img = Image.open(BytesIO(requests.get(url, stream=True).content))
    # async with httpx.AsyncClient() as client:
    #     res = await client.get(url, timeout=100)#, stream=True)
    res = requests.get(url=url, timeout=100, stream=True)
    img = Image.open(BytesIO(res.raw.read()))
    membuf = BytesIO()
    img.save(membuf, format="png")

    membuf.seek(0)
    if save_as:
        img.save(f'{save_as}.png', format="png")
    return membuf

async def analyse_image_azure(url):
    url = await url_to_stream(url)
    image_analysis = cv_client.analyze_image_in_stream(url, visual_features=[VisualFeatureTypes.categories],
                                                       details=[Details.celebrities])
    return image_analysis

async def update_file_with_azure_analytics(persons, img_id, img_url):
    logger.info(f"Updating the file with azure analytics")
    file_type = await get_file_info(img_id, "fileType")
    file_type = file_type["fileType"]
    logger.info(f"file_type: {file_type}")
    if file_type == "image":
        logger.info(f"Processing the image file with the azure analytics for the image id {img_id} and url {img_url}")
        try:
            
            if IS_ON_PREM:
                img_url = auto_move_file_to_temp(img_url)
            else:
                img_url = await make_url_public(img_id,img_url)
            logger.info(f"Updating the file with azure analytics for the image id {img_id} and url {img_url}")
            azure_analytics = await analyse_image_azure(img_url)
            celebs_detected = []
            logger.info(f"azure_analytics: {azure_analytics}")
            for cat in azure_analytics.categories:
                # Check if cat.detail exists and has an attribute 'celebrities'
                if hasattr(cat, 'detail') and cat.detail is not None and hasattr(cat.detail, 'celebrities'):
                    for celeb in cat.detail.celebrities:
                        name = celeb.name
                        confidence = celeb.confidence
                        # Assuming celeb.face_rectangle is guaranteed to exist if celeb exists
                        left, top, width, height = celeb.face_rectangle.left, celeb.face_rectangle.top, celeb.face_rectangle.width, celeb.face_rectangle.height
                        item = {"name": name, "confidence": confidence, "face_rectangle": {"left": left, "top": top, "width": width, "height": height}}
                        celebs_detected.append(item)

            # remove the duplicates from the celebs_detected
            celebs_detected = {json.dumps(d, sort_keys=True) for d in celebs_detected}
            celebs_detected = [json.loads(d) for d in celebs_detected]

            logger.info(f"celebs_detected from azure: {celebs_detected}")

            # update the file info with the azure analytics
            file_type = await get_file_info(img_id, "fileType")
            update_payload = {"azure_analytics":celebs_detected}
            try:
                res = await update_file(img_id,update_payload)
                if res:
                    logger.info(f"Updated the file info with azure analytics")
                else:
                    logger.error(f"Error in updating the file info with azure analytics")
            except Exception as e:
                logger.error(f"Error in updating the file info with azure analytics: {e}")
            
            # print the bboxes from persons in the file
            persons_in_file_server = []
            for person in persons:
                if "face_rectangle" in person:
                    face_rectangle = person["face_rectangle"]
                    item = {"faceId":person["face_id"],
                            "face_rectangle":face_rectangle}
                    persons_in_file_server.append(item)
            logger.info(f"persons_in_file_server: {persons_in_file_server}")

            #map the celebs_detected with the persons_in_file_server
            mapping = map_names_to_faceIds(celebs_detected,persons_in_file_server) 
            logger.info(f"mapping: {mapping}")
            for item in mapping:
                if item['name'] != "Not matched":
                    face_id = item['faceId']
                    # update the face info with the celebrity name
                    update_payload = {"Azure_personName":item['name']}
                    try:
                        res = await update_file(face_id,update_payload)
                        if res:
                            logger.info(f"Updated the face crop info with celebrity name {item['name']}")
                        else:
                            logger.error(f"Error in updating the file info with celebrity name {item['name']}")
                    except Exception as e:
                        logger.error(f"Error in updating the file info with celebrity name {item['name']}: {e}")
            return mapping
        except Exception as e:
            logger.error(f"Error in updating the file with azure analytics: {e}")
            return None
    elif file_type == "video":
        logger.info(f"Processing azure analytics for the video file")
        # logger.info(f"persons: {persons}")
        # process and get the azure analytics for all the persons in the video
        mapping =[]
        for person in persons:
            print(f"----face_url: {person['fileUrl']}")
            face_url = person["fileUrl"]
            face_id = person["face_id"]
            print(f"face_url: {face_url}")
            print(f"@@face_url: {person['fileUrl']}")
            if IS_ON_PREM:
                face_url = auto_move_file_to_temp(face_url)
            else:
                face_url = await make_url_public(face_id,face_url)
            logger.info(f"Processing the face crop with the azure analytics for the face id {face_id} and url {face_url}")
            azure_analytics = await analyse_image_azure(face_url)
            celebs_detected = []
            for cat in azure_analytics.categories:
                # Check if cat.detail exists and has an attribute 'celebrities'
                if hasattr(cat, 'detail') and cat.detail is not None and hasattr(cat.detail, 'celebrities'):
                    for celeb in cat.detail.celebrities:
                        name = celeb.name
                        confidence = celeb.confidence
                        # Assuming celeb.face_rectangle is guaranteed to exist if celeb exists
                        left, top, width, height = celeb.face_rectangle.left, celeb.face_rectangle.top, celeb.face_rectangle.width, celeb.face_rectangle.height
                        item = {"name": name, "confidence": confidence, "face_rectangle": {"left": left, "top": top, "width": width, "height": height}}
                        celebs_detected.append(item)
            # remove the duplicates from the celebs_detected
            celebs_detected = {json.dumps(d, sort_keys=True) for d in celebs_detected}
            celebs_detected = [json.loads(d) for d in celebs_detected]
            logger.info(f"celebs_detected from azure: {celebs_detected}")
            celeb_name = "Unknown"
            if len(celebs_detected) > 0:
                celeb_name = celebs_detected[0]["name"]
            # update the face info with the celebrity name
            update_payload = {"Azure_personName":celeb_name}
            try:
                res = await update_file(face_id,update_payload)
                if res:
                    logger.info(f"Updated the face crop info with celebrity name {celeb_name}")
                else:
                    logger.error(f"Error in updating the file info with celebrity name {celeb_name}")
            except Exception as e:
                logger.error(f"Error in updating the file info with celebrity name {celeb_name}: {e}")

            mapping.append({"name":celeb_name,"faceId":face_id,"distance":1.0})
        logger.info(f"mapping: {mapping}")
        return mapping

def map_names_to_faceIds(service_1_bboxes, service_2_bboxes):
    # Calculate center points for Service 1 and Service 2 bounding boxes
    service_1_centers = [{**bbox, 'center': (bbox['face_rectangle']['left'] + bbox['face_rectangle']['width'] / 2, bbox['face_rectangle']['top'] + bbox['face_rectangle']['height'] / 2)} for bbox in service_1_bboxes]
    service_2_centers = [{**bbox, 'center': ((bbox['face_rectangle']['left'] + bbox['face_rectangle']['right']) / 2, (bbox['face_rectangle']['top'] + bbox['face_rectangle']['bottom']) / 2)} for bbox in service_2_bboxes]

    # Function to calculate Euclidean distance between centers
    def distance(center1, center2):
        return ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5

    # Initialize mappings and unmatched face IDs
    mappings = []
    unmatched_face_ids = [s2['faceId'] for s2 in service_2_centers]

    # Compare all Service 1 centers with Service 2 centers to find the closest matches
    for s1 in service_1_centers:
        closest_match = None
        for s2 in service_2_centers:
            if s2['faceId'] in unmatched_face_ids:  # Ensure s2 has not been matched already

                # Dynamic threshold based on the current s2 bounding box size
                width = s2['face_rectangle']['right'] - s2['face_rectangle']['left']
                height = s2['face_rectangle']['bottom'] - s2['face_rectangle']['top']
                dynamic_threshold = (width + height) / 2 / 2 # atleast 1/2th of the average width and height


                dist = distance(s1['center'], s2['center'])
                if dist < dynamic_threshold:
                    if closest_match is None or (dist < closest_match['distance']):
                        closest_match = {'name': s1['name'], 'faceId': s2['faceId'], 'distance': dist}

        if closest_match:
            mappings.append(closest_match)
            unmatched_face_ids.remove(closest_match['faceId'])  # Remove matched face ID from unmatched list

    # Add "Not matched" for remaining unmatched face IDs
    for face_id in unmatched_face_ids:
        mappings.append({'name': 'Unknown', 'faceId': face_id, 'distance': 1.0})

    return mappings


async def get_images_tagged_to_entity(entity_id):
    """
    Fetches the face mapping for the given entity_id. So that we can get the list of face_ids that are tagged to the entity card by FR
    """

    try:
        url = f"{DOC_SERVER_BASE_URL}/files/"
        query_params = {
            'query': f"Face_Mapping.entity_id: \"{entity_id}\"",
            'fieldsToReturn': 'Face_Mapping'
        }
        headers = {
            'accept': 'application/json'
        }
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=query_params, headers=headers)
        # response = requests.get(url, params=query_params, headers=headers)
        
        
        if response.status_code == 200:
            # Assuming the response content is JSON and you want to parse it
            response_json =  response.json()
            return response_json
        else:
            return {'error': 'Failed to fetch data', 'status_code': response.status_code}
    except Exception as e:
        return {'error': 'Failed to fetch data', 'exception': str(e)}
    
async def fetch_unprocessed_relationship_files(items_per_page = 20, fields_to_return = ""):
    try:
        url = f"{DOC_SERVER_BASE_URL}/files/"
        query = '_fileProcessorStatus.FacialRecognition: "processed" AND NOT _fileProcessorStatus.RelationShipStatus:"processed"'
        if fields_to_return == "":
            query_params = {
                'query': query,
                'sortByField': "-_createdDate",
                'itemsPerPage': str(items_per_page)
            }
        else:
            query_params = {
                'query': query,
                'sortByField': "-_createdDate",
                'itemsPerPage': str(items_per_page),
                'fieldsToReturn': fields_to_return
            }
        headers = {
            'accept': 'application/json'
        }
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=query_params, headers=headers)
        # response = requests.get(url, params=query_params, headers=headers)
        
        if response.status_code == 200:
            # Assuming the response content is JSON and you want to parse it
            return response.json()
        else:
            # Provide basic error handling
            return {'error': 'Failed to fetch data', 'status_code': response.status_code}
    except Exception as e:
        return {'error while fetching unprocessed relationship files': str(e)}
    

async def construct_vdb_payload_from_file_info(fileId, fileUrl, persons, num_faces_final, fileType):
    """
    Construct the vdb payload from the persons info
    """

    temp_persons = []
    if type(persons) == list: # for images
        for person in persons:
            vdb_entity = {}
            vdb_entity["face_id"] = person["face_id"]
            vdb_entity["face_url"] = person["fileUrl"]
            vdb_entity["face_embeddings"] = person["face_embedding"]
            vdb_entity["img_id"] = fileId
            vdb_entity["img_url"] = await make_url_public(fileId, fileUrl)
            vdb_entity["entity_id"] = "Default"
            vdb_entity["event_id"] = "Default"
            vdb_entity["crawler"] = "Default"
            vdb_entity["blur_score"] = person["blur_score"]
            vdb_entity["alignment_score"] = person["alignment_score"]
            vdb_entity["is_profile_image"] = person["is_profile_image"]
            vdb_entity["num_faces"] = num_faces_final
            vdb_entity["source_type"] = fileType
            temp_persons.append(vdb_entity)

        return {"entities": temp_persons, "collection_name": VECTOR_DB_FACE_COLECTION, "enable_flush": True}
    
async def get_crawler_from_file_id(file_id, items_per_page=1, page_number=1):
    query = f'Attachments.FileId : "{file_id}"'
    params = {
        'query': query,
        'itemsPerPage': str(items_per_page),
        'pageNumber': str(page_number)
    }
    headers = {
        'accept': 'application/json'
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(DOC_SERVER_DOC_COLLECTION_URL, headers=headers, params=params)
    # response = requests.get(DOC_SERVER_DOC_COLLECTION_URL, headers=headers, params=params)
    if response.status_code == 200:
        response_data = response.json()
        if 'documents' in response_data and response_data['documents']:
            first_document = response_data['documents'][0]
            crawler_data  = None
            if '_crawler' in first_document:
                crawler_data = first_document['_crawler']
            if crawler_data:
                return crawler_data
            else:
                return "Default"

        #return response.json()['documents'][0]['_crawler']
    else:
        return f"Error: {response.status_code}, {response.text}"
    
async def save_clip_embeddings_docserver(data):

    url = f"{DOC_SERVER_BASE_URL}/docs/clip_embeddings"
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }

    # Ensure data is a JSON-formatted string
    if not isinstance(data, str):
        data = json.dumps(data)
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, data=data)
    # response = requests.post(url, headers=headers, data=data)
    return response.json()

async def process_clip_embeddings(payload, file_info, get_crawler_from_file_id, CLIP_EMBEDDING_INSERT_URL, save_clip_embeddings_docserver, update_file, logger):
    entity_id = file_info.get("_entityIds", "Default")
    if isinstance(entity_id, list) and entity_id:
        entity_id = entity_id[0]
    elif isinstance(entity_id, str):
        entity_id = entity_id
    if not entity_id:
        entity_id = "Default"

    event_id = file_info.get("_eventIds", "Default")
    if isinstance(event_id, list) and event_id:
        event_id = event_id[0]
    elif isinstance(event_id, str):
        event_id = event_id
    if not event_id:
        event_id = "Default"

    crawler_data = await get_crawler_from_file_id(payload.fileId) or "Default"
    if isinstance(crawler_data, list) and crawler_data:
        crawler_data = crawler_data[0]
    elif isinstance(crawler_data, str):
        crawler_data = crawler_data
    if not crawler_data:
        crawler_data = "Default"

    clip_embedding_payload = {
        "fileId": payload.fileId,
        "file_url": payload.file_url,
        "entity_id": entity_id,
        "event_id": event_id,
        "crawler": crawler_data,
        "flush": True
    }
    try:
        async with httpx.AsyncClient() as client:
            clip_insert_res = await client.post(CLIP_EMBEDDING_INSERT_URL, json=clip_embedding_payload)

        # clip_insert_res = requests.post(CLIP_EMBEDDING_INSERT_URL, json=clip_embedding_payload)
        if clip_insert_res.status_code == 200:
            response_json = clip_insert_res.json()
            error_message = response_json.get("error")
            if error_message:
                Patch_payload = {"_fileProcessorStatus": {"ClipEmbedding": f"Error: {error_message}"}}
            if response_json.get("status") == "successfully inserted entity into vector db":
                logger.info(f"Successfully inserted clip embedding for file_id: {payload.fileId}")
                vdb_payload = {"_vdbPayload": response_json["vdb_payload"]}
                response_ = await save_clip_embeddings_docserver(vdb_payload)
                if response_ and response_.get("_status") == "created":
                    doc_id = response_["_id"]
                Patch_payload = {"_fileProcessorStatus": {"ClipEmbedding": "processed"}, "_vdbDocId": doc_id}
            logger.info(f"Updating file_id: {payload.fileId} with payload: {Patch_payload}")
            await update_file(payload.fileId, Patch_payload)
        else:
            logger.info(f"Failed to insert clip embedding for file_id: {payload.fileId}")
            error_message = f"Failed to insert clip embedding for file_id: {payload.fileId}"
            Patch_payload = {"_fileProcessorStatus": {"ClipEmbedding": f"Error: {error_message}"}}
            await update_file(payload.fileId, Patch_payload)
    except Exception as e:
        logger.info(f"Failed to insert clip embedding for file_id: {payload.fileId}")
        error_message = f"Failed to insert clip embedding for file_id: {payload.fileId}"
        Patch_payload = {"_fileProcessorStatus": {"ClipEmbedding": f"Error: {error_message}"}}
        await update_file(payload.fileId, Patch_payload)


async def update_face_id_with_google_lens_person_name(face_id, google_lens_person_name):
    patch_data = {
        "GoogleLens_personName": google_lens_person_name
    }
    try:
        res = await update_file(face_id, patch_data)
        logger.info(f"Updated the face_id {face_id} with google lens person name {google_lens_person_name}")
    except Exception as e:
        logger.error(f"Error in updating the face_id {face_id} with google lens person name {google_lens_person_name}: {e}")

def get_celebrity_list_from_google_lens_response(response):
    celebs_detected = []
    if 'related_search' in response:
        related_search = response['related_search']
        for item in related_search:
            if 'title' in item:
                celebs_detected.append(item['title'])
    return celebs_detected

def update_file_with_google_lens_analysis(file_id, response):
    response_json = response
    exact_matches_main_image = []
    recognized_text_main_image = []
    related_search_main_image = []
    images_main_image = []
    if response_json and "exact_matches" in response_json:
        exact_matches_main_image = response_json["exact_matches"]
    if response_json and "recognized_text" in response_json:
        recognized_text_main_image = response_json["recognized_text"]
    if response_json and "related_search" in response_json:
        related_search_main_image = response_json["related_search"]
    if response_json and "images" in response_json:
        images_main_image = response_json["images"]
    patch_data = {
        "GoogleLens_exact_matches": exact_matches_main_image,
        "GoogleLens_recognized_text": recognized_text_main_image,
        "GoogleLens_related_search": related_search_main_image,
        "GoogleLens_images": images_main_image
    }
    try:
        res = update_file(file_id, patch_data)
        logger.info(f"Updated the file_id {file_id} with google lens analysis")
    except Exception as e:
        logger.error(f"Error in updating the file_id {file_id} with google lens analysis: {e}")

async def update_entity_card_person_name(entity_id, person_name):
    try:
        url = f"{DOC_SERVER_BASE_URL}/docs/EntityCard/{entity_id}"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        data = {"textFullName": person_name}
        async with httpx.AsyncClient() as client:
            response = await client.patch(url, json=data, headers=headers, timeout=TIMEOUT_IN_SEC)
        # response = requests.patch(url, json=data, headers=headers, timeout=TIMEOUT_IN_SEC)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to update the entity card with person name {person_name} for entity_id {entity_id} with status code {response.status_code} and response {response.text}")
            return None
    except Exception as e:
        logger.error(f"Error in updating the entity card with person name {person_name} for entity_id {entity_id}: {e}")
        return None

async def get_similar_images_relationships_from_image_id(image_id):
    try:
        url = 'http://relationship-server.cyber.svc.cluster.local:8000/api/GetSimilarImagesFromImage'
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json'
        }
        payload = {
            "entity": {
                "_id": image_id
            },
            "options": {
                "relTypes": [],
                "pageNumber": 1,
                "itemsPerPage": 500
            }
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, data=json.dumps(payload))
        # response = requests.post(url, headers=headers, data=json.dumps(payload))
        return response.json()
    except Exception as e:
        logger.error(f"Failed to get similar images relationships from image id {image_id} with exception {e}")
        return None
    
async def clip_search_image_from_file_id(file_id):
    try:
        url = 'http://cyber-clip-embeddings-microservice.cyber.svc.cluster.local/ImageSearchFromImageFileId'
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json'
        }
        data = {
            "file_id": file_id
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, data=json.dumps(data))
        # response = requests.post(url, headers=headers, data=json.dumps(data))
        return response.json()  # Assuming the response JSON contains the data you need
    except Exception as e:
        logger.error(f"Failed to clip search image from file id {file_id} with exception {e}")
        return None

def get_entity_from_Face_Entity_Mapping(Face_Entity_Mapping, face_id):
    person_name = "Unknown"
    entity_id = "Default"

    # old Face_Entity_Mapping format
    if Face_Entity_Mapping and face_id in Face_Entity_Mapping:
        person_name = Face_Entity_Mapping[face_id].get("person_name", "Unknown")
    if Face_Entity_Mapping and face_id in Face_Entity_Mapping and "entity_id" in Face_Entity_Mapping[face_id]:
        entity_id = Face_Entity_Mapping[face_id].get("entity_id", "Default")
    # new Face_Entity_Mapping format
    if Face_Entity_Mapping and "face_id" in Face_Entity_Mapping:
        person_name = Face_Entity_Mapping.get("person_name", "Unknown")
    if Face_Entity_Mapping and "entity_id" in Face_Entity_Mapping:
        entity_id = Face_Entity_Mapping.get("entity_id", "Default")
    return person_name, entity_id


async def update_Face_Entity_Mapping(file_id, person_name):
    try:
        file_info = await get_file_info(file_id, fields_to_return='Face_Entity_Mapping')
        face_entity_mapping = {}
        if file_info and "Face_Entity_Mapping" in file_info:
            face_entity_mapping = file_info["Face_Entity_Mapping"]
        
        url = f"{DOC_SERVER_BASE_URL}/files/{file_id}"
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json'
        }
        # new Face_Entity_Mapping format
        if face_entity_mapping:
            payload = {
                "Face_Entity_Mapping": {
                    "person_name": person_name
                }
            }
        elif not face_entity_mapping:
            payload = {
                "Face_Entity_Mapping": {
                    "person_name": person_name
                }
            }
        # old Face_Entity_Mapping format
        elif file_id in face_entity_mapping:
            payload = {
                "Face_Entity_Mapping": {
                    file_id: {
                        "person_name": person_name
                    }
                }
            }
        async with httpx.AsyncClient() as client:
            response = await client.patch(url, headers=headers, data=json.dumps(payload))
        # response = requests.patch(url, headers=headers, data=json.dumps(payload))
        return response.json()
    except Exception as e:
        logger.error(f"Failed to update person name for file id {file_id} with exception {e}")
        return None
    
async def create_image_entitycard_if_doesnt_exist(doc_id):
    url = f"{DOC_SERVER_BASE_URL}/docs/EntityCard/{doc_id}"
    headers = {
        "accept": "application/json"
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
    # response = requests.get(url, headers=headers)
    response_json = response.json()
    if response_json:
        return response_json
    else:
        # read the file from file collection and create EntityCard witht the collected information
        file_info = await get_file_info(doc_id, fields_to_return='fileUrl')
        image_entity = {
            "_id": doc_id,
            "entityType": "IMAGE",
            "nodeId": f'IMAGE_{doc_id}',
            "_uniqueKeyFields": "nodeId",
            "Schema": "Entity",
            "textFullName": f'IMAGE_{doc_id}',
            "Url" : file_info['fileUrl'],
            "profileImageUrl" : file_info['fileUrl']
        }
        image_entity_card = await create_entity_card(image_entity)
        return image_entity
    

async def get_files(query, fieldsToReturn=None, items_per_page=10, page_number=1, sortByField= "-_createdDate"):
    url = 'http://cyber-docserver-v2-microservice.cyber.svc.cluster.local/files'
    
    # Define the parameters for the GET request
    params = {
        'query': query,
        'itemsPerPage': items_per_page,
        'pageNumber': page_number,
        'sortByField': sortByField,
    }

    if fieldsToReturn is not None:
        params['fieldsToReturn'] = fieldsToReturn
    
    # Define the headers for the GET request
    headers = {
        'accept': 'application/json'
    }
    
    # Send the GET request
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, params=params)
    # response = requests.get(url, headers=headers, params=params)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()  # Return the JSON response
    else:
        response.raise_for_status()  # Raise an exception for other status codes


async def get_files_v1(query, fieldsToReturn=None, items_per_page=10, page_number=1, sortByField= "-_createdDate"):
    url = "http://docserver.cyber.svc.cluster.local:5000/docserver/files/"
    
    # Define the parameters for the GET request
    params = {
        'query': query,
        'itemsPerPage': items_per_page,
        'pageNumber': page_number,
        'sortByField': sortByField,
    }

    if fieldsToReturn is not None:
        params['fieldsToReturn'] = fieldsToReturn
    
    # Define the headers for the GET request
    headers = {
        'accept': 'application/json'
    }
    
    # Send the GET request
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, params=params)
    # response = requests.get(url, headers=headers, params=params)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()  # Return the JSON response
    else:
        response.raise_for_status()  # Raise an exception for other status codes

async def get_documents(collection, query, fields_to_return, sort_by_field, items_per_page):
    # Base URL
    base_url = f"http://docserver.cyber.svc.cluster.local:5000/docserver/docs/{collection}"
    
    # Construct the full URL with query parameters
    url = f"{base_url}?query={query}&fieldsToReturn={fields_to_return}&sortByField={sort_by_field}&itemsPerPage={items_per_page}"
    
    # Set the headers
    headers = {
        "accept": "application/json"
    }
    
    # Perform the GET request
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)

    # response = requests.get(url, headers=headers)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()

async def get_document_by_id(collection, document_id, fields_to_return):
    # Base URL
    base_url = f"http://docserver.cyber.svc.cluster.local:5000/docserver/docs/{collection}/{document_id}"
    
    # Construct the full URL with query parameters
    url = f"{base_url}?fieldsToReturn={fields_to_return}"
    
    # Set the headers
    headers = {
        "accept": "application/json"
    }
    
    # Perform the GET request
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers)
    # response = requests.get(url, headers=headers)
    
    # Check if the request was successful
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()

async def reconstruct_face_entity_mapping(face_id,file_info):
    # converts Face_Entity_Mapping from old format to new
    file_info = await get_file_info(face_id, fields_to_return='Face_Entity_Mapping')
    face_entity_mapping = {}
    if file_info and "Face_Entity_Mapping" in file_info:
        face_entity_mapping = file_info["Face_Entity_Mapping"]

    # if it is old format
    face_mapping = {}
    if face_id in face_entity_mapping.keys():
        face_mapping = face_entity_mapping[face_id]
        file_info["Face_Entity_Mapping"] = face_mapping
    return file_info
        
async def get_files_tagged_to_entity_id_in_Face_Entity_Mapping(entity_id, items_per_page=10, page_number=1, fieldsToReturn=None):
    base_url = 'http://cyber-docserver-v2-microservice.cyber.svc.cluster.local/files'
    query_param = f'Face_Entity_Mapping.entity_id:"{entity_id}"'
    params = {
        'query': query_param,
        'itemsPerPage': items_per_page,
        'pageNumber': page_number,
    }
    if fieldsToReturn:
        params['fieldsToReturn'] = fieldsToReturn
    headers = {
        'accept': 'application/json'
    }
    async with httpx.AsyncClient() as client:
        response = await client.get(base_url, params=params, headers=headers)
    # response = requests.get(base_url, params=params, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        response.raise_for_status()

async def update_thumbnail(fileId, file_url):
    logger.info(f"Updating thumbnail for fileId {fileId} with file url {file_url}")
    headers = {
        "CF-Access-Client-Id": CF_ACCESS_CLIENT_ID,
        "CF-Access-Client-Secret": CF_ACCESS_CLIENT_SECRET
    }
    video_path = f"{fileId}.mp4"
    thumbnail_path = f"{fileId}.jpg"
    max_retries = 3

    try:
        for attempt in range(max_retries):
            try:
                # Download the video file
                async with httpx.AsyncClient() as client:
                    response = await client.get(file_url, headers=headers, follow_redirects=True, timeout=120)
                    response.raise_for_status()

                # Save the video content to a file
                with open(video_path, "wb") as f:
                    f.write(response.content)

                # Verify the downloaded video file size
                if os.path.getsize(video_path) == 0:
                    logger.error(f"Downloaded video file is empty for fileId {fileId}")
                    continue

                # Extract the first frame from the video
                video = cv2.VideoCapture(video_path)
                if not video.isOpened():
                    logger.error(f"Failed to open video file: {video_path}")
                    continue

                success, image = video.read()
                video.release()

                # Check if the frame was successfully extracted
                if not success or image is None:
                    logger.error(f"Failed to read frame from video for fileId {fileId}")
                    continue

                # Write the thumbnail image
                cv2.imwrite(thumbnail_path, image)

                # Upload the thumbnail
                file_name = f"{fileId}_thumbnail.jpg"
                upload_response = await upload_file(thumbnail_path, file_name)

                # Clean up temporary files
                if os.path.exists(video_path):
                    os.remove(video_path)
                if os.path.exists(thumbnail_path):
                    os.remove(thumbnail_path)

                # Update the file metadata if upload is successful
                if upload_response:
                    thumbnail_url = upload_response["fileUrl"]
                    patch_data = {"thumbnail_url": thumbnail_url}
                    res = await update_file(fileId, patch_data)
                    if res:
                        logger.info(f"Updated thumbnail for fileId {fileId}")
                        return res
                    else:
                        logger.error(f"Failed to update thumbnail URL for fileId {fileId}")

            except httpx.RequestError as e:
                logger.error(f"Request error for fileId {fileId} on attempt {attempt + 1}: {e}")
            except Exception as e:
                logger.error(f"Error in updating thumbnail for fileId {fileId} on attempt {attempt + 1}: {e}")

        logger.error(f"Failed to update thumbnail for fileId {fileId} after {max_retries} attempts")
        return None

    except Exception as e:
        logger.error(f"Unexpected error in update_thumbnail for fileId {fileId}: {e}")
        return None




