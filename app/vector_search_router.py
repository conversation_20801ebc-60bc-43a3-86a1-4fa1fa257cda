from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from collections import Counter

from .dependencies import *
# from config import *
from .utils import *
from .config import *
import logging
import httpx
import os
import numpy as np
import time
from .ip_utils import get_client_ip

DEBUG_MODE = os.getenv('DEBUG_MODE', False)
logging.basicConfig(
    datefmt='%m/%d/%y | %I:%M:%S %p',
    format='%(asctime)s| %(threadName)s| %(levelname)s| %(lineno)3s| %(filename)s: %(message)s',
    handlers=[logging.StreamHandler()]
)
logging.info('logger initialized')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)

vector_search_router = APIRouter()


class SimilaritySearchPayload(BaseModel):
    file_url: str
    fileId: str
    entity_id: Optional[str] = "Default"
    top_k: Optional[int] = 10
    threshold: Optional[float] = 0.5
    expr: Optional[str] = None

class InputRequestPayload(BaseModel):
    file_url: str
    fileId: str
    img_id: Optional[str] = None
    img_url: Optional[str] = None
    person_data: Optional[Any] = None
class CheckSimilarityRequestPayload(BaseModel):
    file_id_1: str
    file_id_2: str
    file_url_1: str
    file_url_2: str
    similarity_threshold: Optional[float] = 0.38

class FaceDataItem(BaseModel):
    faceId: str
    fileUrl: str

class FaceFileItem(BaseModel):
    faceId: str
    fileId: str

class CommonImageRelationshipPayload(BaseModel):
    persons: List[FaceFileItem]
    top_k: Optional[int] = 100

class FileDataItem(BaseModel):
    fileId: str
    fileUrl: str

class TargetorCaseDataItem(BaseModel):
    targetId: Optional[str] = 'Default'
    caseId: Optional[str] = 'Default'

class FaceDataPayload(BaseModel):
    faceData: List[FaceDataItem]
    entity_id: str
    top_k: int
    threshold: float
    expr : Optional[str] = None
    imageId: str
    imageUrl: str
    file_type: Optional[str] = "Default"
    person_data: Optional[Any] = None
    range_filter: Optional[float] = 0.999988
    class Config:
        schema_extra = {
            "example": {
                "faceData": [
                    {
                        "faceId": "659e1b605757f53441ad7f06",
                        "fileUrl": "/files/File/659e1b605757f53441ad7f06/face_196265fa883872cc2be83760be67a52c_0.jpg"
                    },
                    # ... other faceData items
                ],
                "entity_id": "Default",
                "top_k": 10,
                "threshold": 0.5,
                "imageId": "658d1df006389c2a53b77de4",
                "imageUrl": "https://cybersmart-dev.webint.pro/fs/files2/File/658d1df006389c2a53b77de4/NRIC_testsample.jpg"
            }
        }

class FrBackupPayload(BaseModel):
    BackupId: int
    CollectionName: str

class EntityCard(BaseModel):
    entity_id: str

async def get_person_name_vdb(payload: InputRequestPayload):
    face_id = payload.fileId
    face_url = payload.file_url
    img_id = payload.img_id
    person_data = payload.person_data

    # search the personName in the file, incase if the personName is already registered
    person_name_in_face_info = ""
    face_info = await get_file_info(face_id,"personName,Azure_personName,Face_Entity_Mapping")
    if face_info and "personName" in face_info:
        person_name_in_face_info = face_info["personName"]
    if person_name_in_face_info=="" and face_info and "Azure_personName" in face_info:
        person_name_in_face_info = face_info["Azure_personName"]
    if person_name_in_face_info=="" and face_info and "Face_Entity_Mapping" in face_info:
        face_entity_mapping = face_info["Face_Entity_Mapping"]
        # old Face_Entity_Mapping format
        if face_id in face_entity_mapping:
            if face_entity_mapping[face_id]['person_name']!="Unknown":
                person_name_in_face_info = face_entity_mapping[face_id]["person_name"]
        # new Face_Entity_Mapping format
        if "face_id" in face_entity_mapping:
            if face_id == face_entity_mapping["face_id"]:
                if face_entity_mapping['person_name']!="Unknown":
                    person_name_in_face_info = face_entity_mapping['person_name']


    # logger.info(f"Fetching similar images for the face {face_id}")
    #Get similar images of the face
    similar_images_payload = {
                                "faceData": [{
                                        "faceId": face_id,
                                        "fileUrl": "",
                                    }
                                ],
                                "entity_id": "Default",
                                "top_k": 10,
                                "threshold": 0.65,
                                "imageId": img_id,
                                "imageUrl": "",
                                "person_data": person_data
                            }
    # vector_search_by_face_url = "http://localhost:8000/VectorSearch/get-similar-images-by-face/"
    face_data_payload = FaceDataPayload(faceData=[FaceDataItem(faceId=face_id,fileUrl="")],entity_id="Default",top_k=10,threshold=0.65,imageId=img_id,imageUrl="",person_data=person_data)
    
    # read the file info for the face_id
    person_name_found = False
    if person_name_found==False:
        try:
            async with httpx.AsyncClient() as client:
                
                # response = await client.post(vector_search_by_face_url, json=similar_images_payload, timeout=30)
                # response.raise_for_status()
                # similar_images = response.json()
                similar_images   = await get_similar_images_by_face(face_data_payload)
                search_results = similar_images.get("search_results",[])
                similar_images = search_results[face_id]["similar_images"]
                filtered = []
                for item in similar_images:
                    distance = item["distance"]
                    if distance>= 0.5:
                        filtered.append(item["face_id"])
                # get file info for the filtered similar images
                logger.info(f"Filtered similar images: {filtered}")
                person_names_found = []
                if person_name_in_face_info != "":
                    person_names_found.append(person_name_in_face_info)
                else:
                    for face in filtered:
                        face_info = await get_file_info(face,"personName")
                        if face_info and "personName" in face_info:
                            if face_info["personName"] != "":
                                person_names_found.append(face_info["personName"])
                logger.info(f"Person names found in similar images: {person_names_found}")
                # get the most common person name
                if len(person_names_found)>0:
                    person_name = Counter(person_names_found).most_common(1)[0][0]

                    person_name_found = True
                    return {"person_name": person_name, "registered":False, "similar_images":similar_images}
                else:
                    print("personName not found in face_info")
                    print(f"Person name found: {person_name_found}")
                    return {"similar_images":similar_images}
        except Exception as e:
            logger.error(f"Error in fetching similar images for {face_id}: {e}")
            return {"error":str(e)} 

@vector_search_router.post("/get-similar-images/", tags=["Vector Search"])
async def get_similar_images(payload: SimilaritySearchPayload, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Given an image, identify the people in the image, return the similar images of the people from the vector Database
    """
    # check if the image is already in the vector db
    # if not, process the image and insert into vector db
    # if yes, get the embeddings from vector db and search for similar images
    # return the similar images
    file_url = payload.file_url
    fileId = payload.fileId
    entity_id = payload.entity_id
    top_k = payload.top_k
    threshold = payload.threshold
    headers={"CF-Access-Client-Id": "084161eb89f7b63ed356bdb11cc415f3.access",
                                            "CF-Access-Client-Secret": "918ae96432f5ba9e69547900ea36a7618595ce83393cd4aa6cd5308edae76dbe"}
    # query the vector db for the image
    try:
        query_payload = {
            "collection_name": VECTOR_DB_FACE_COLECTION,
            "search_field": "img_id",
            "offset": 0,
            "limit": 10,
            "query_values": [fileId],
            "query_out_fields": ["face_id", "face_embeddings","face_url"]
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(VECTOR_DB_QUERY_URL, json=query_payload)
        # response = requests.post(VECTOR_DB_QUERY_URL, json=query_payload)
        response.raise_for_status()
        response_json = response.json()["results"]

        if response_json and len(response_json) > 0:
            #found more than 1 face for the image
            logger.info(f"Found {len(response_json)} faces for the image {fileId} in the Vector DB")
            face_embeddings_list = []
            face_url_list = []
            search_results = {}
            for result in response_json:
                face_embeddings_list.append(result['face_embeddings'])
                face_url = result['face_url']
                face_url_list.append(face_url)
            #     search_payload = {
            #         "collection_name": "face_collection",
            #         "vector_field_name": "face_embeddings",
            #         "query_vectors": [result['face_embeddings']],
            #         "top_k": top_k,
            #         #"search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : 0.7}},
            #         "search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : threshold, "range_filter" : 0.999988}},  # range_filter is used to filter out the exact 100% match
            #         "fields_to_return": ["face_id", "img_id","face_url", "img_url","entity_id"]
            #     }
            #     response = requests.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
            #     if response.status_code == 200:
            #         response_dict = json.loads(response.text)
            #         results = response_dict["results"]
            #         search_results_list.append(results)
            search_payload = {
                    "collection_name": VECTOR_DB_FACE_COLECTION,
                    "vector_field_name": "face_embeddings",
                    "query_vectors": face_embeddings_list,
                    "top_k": top_k,
                    #"search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : 0.7}},
                    "search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : threshold, "range_filter" : 0.999988}},  # range_filter is used to filter out the exact 100% match
                    "fields_to_return":  ["face_id", "img_id","face_url", "img_url","entity_id","source_type"]
                }
            async with httpx.AsyncClient() as client:
                response = await client.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
            # response = requests.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
            if response.status_code == 200:
                response_dict = json.loads(response.text)
                results = response_dict["results"]
                for key in results.keys():
                    img_ids = [d['img_id'] for d in results[key]]
                    entity_ids = [d['entity_id'] for d in results[key]]
                    img_ids_without_entity_id = []
                    filered_entity_ids = []
                    for img_id, entity_id in zip(img_ids, entity_ids):
                        if entity_id == 'Default' or entity_id == '' or entity_id == None or entity_id == 'string':
                            logger.info(f"Entity id is not provided for image {img_id}, checking the docserver for entity id")
                            try:
                                #logger.info("!!!!!!!!!!")
                                file_info = await get_file(img_id)
                                #logger.info(f"Entity id is {file_info['_entityIds']}")
                                # cehck if _entityIds is present in the file_info
                                if '_entityIds' in file_info and file_info['_entityIds'] is not None and len(file_info['_entityIds']) > 0:
                                    entity_id = file_info['_entityIds'][0]
                                    logger.info(f"Entity id found in docserver: {entity_id}")
                                    if entity_id != "Default" and entity_id != "" and entity_id != None and entity_id != "string":
                                        filered_entity_ids.append(entity_id)
                                    # replace the entity id in results
                                    for result in results[key]:
                                        if result['img_id'] == img_id:
                                            result['entity_id'] = entity_id
                            except Exception as e:
                                logger.error(f"Error occurred while getting entity id from docserver: {e}")

                        else:
                            if entity_id != "Default" and entity_id != "" and entity_id != None and entity_id != "string":
                                filered_entity_ids.append(entity_id)

                    entity_id_count = Counter(filered_entity_ids)
                    most_common_entity_id = None
                    highest_count = 0
                    if entity_id_count:
                        most_common_entity_id, highest_count = entity_id_count.most_common(1)[0]
                    most_common_entity_id = {"entity_id": most_common_entity_id, "count": highest_count}
                    if most_common_entity_id!=None:
                        logger.info(f"reading from docserver for the Most common entity is {most_common_entity_id}")
                        #Read the entity name from the docserver
                        try:
                            if most_common_entity_id['entity_id'] != "Default" and most_common_entity_id['entity_id'] != "" and most_common_entity_id['entity_id'] != None and most_common_entity_id['entity_id'] != "string":
                                entity_card = await get_entity_card(most_common_entity_id['entity_id'])
                                if entity_card and 'textFullName' in entity_card:
                                    most_common_entity_id['entity_name'] = entity_card['textFullName']
                                else:
                                    logger.info(f"Entity name not found for entity id {most_common_entity_id['entity_id']}")
                        except Exception as e:
                            logger.error(f"Error occurred while getting entity name from docserver: {e}")

                    face_dict = {"face_url": face_url_list[int(key)], "similar_images": results[key], "entity_id_count": entity_id_count, "most_common_entity_id": most_common_entity_id}
                    search_results[f"face_{key}"] = face_dict

                search_results_list = search_results
    except requests.RequestException as err:
        raise HTTPException(status_code=400, detail="Unable to query vector db")
    
    return {"search_results": search_results_list}

@vector_search_router.post("/get-similar-images-by-face/" , tags=["Vector Search"])
async def get_similar_images_by_face(payload: FaceDataPayload, req: Request ):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Given an image, identify the people in the image, return the similar images of the people from the vector Database
    """
    # logger.info(f" payload:{payload}")

    img_id = payload.imageId
    img_url = payload.imageUrl

    face_data = payload.faceData
    entity_id = payload.entity_id
    top_k = payload.top_k
    threshold = payload.threshold
    range_filter = payload.range_filter
    expr = payload.expr
    face_id_list = []
    face_url_list = []
    face_embeddings_list = []
    
    if payload.person_data:
        persons = payload.person_data
    else: 
        file = await get_file(img_id)
        persons = file['persons']

    temp_persons = []
    if type(persons) == dict:
        for key, value in persons.items():
            if "embedding" in value:
                value['face_embedding'] = value['embedding']
                del value['embedding']
            temp_persons.append(value)
        persons = temp_persons

    for face in face_data:
        for person in persons:
            face_id = face.faceId
            if person['face_id'] == face_id:
                face_id_list.append(face.faceId)
                face_url_list.append(face.fileUrl)
                face_embeddings = person['face_embedding']
                face_embeddings_list.append(face_embeddings)
                break

    search_payload = {
                    "collection_name": VECTOR_DB_FACE_COLECTION,
                    "vector_field_name": "face_embeddings",
                    "query_vectors": face_embeddings_list,
                    "top_k": top_k,
                    #"search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : 0.7}},
                    "search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : threshold, "range_filter" : range_filter}},  # range_filter is used to filter out the exact 100% match
                    "fields_to_return": ["face_id", "img_id","face_url", "img_url","entity_id","source_type"],
                    "expr": expr
                }
    headers={"CF-Access-Client-Id": "084161eb89f7b63ed356bdb11cc415f3.access",
                                        "CF-Access-Client-Secret": "918ae96432f5ba9e69547900ea36a7618595ce83393cd4aa6cd5308edae76dbe"}
    async with httpx.AsyncClient() as client:
        response = await client.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
    # response = requests.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
    search_results = {}
    if response.status_code == 200:
        response_dict = json.loads(response.text)
        results = response_dict["results"]

        for key in results.keys():
            img_ids = [d['img_id'] for d in results[key]]
            entity_ids = [d['entity_id'] for d in results[key]]
            face_ids = [d['face_id'] for d in results[key]]
            distances = [d['distance'] for d in results[key]]
            filered_entity_ids = []
            for face_id, entity_id in zip(face_ids, entity_ids):
                # try to get the entity id from the docserver
                file_info = await get_file_info(face_id,"_entityIds,Face_Entity_Mapping")

                # reconstruct the Face_Entity_Mapping from old structure to new structure
                file_info = await reconstruct_face_entity_mapping(face_id,file_info)

                if file_info  and "Face_Entity_Mapping" in file_info:
                    face_entity_mapping = file_info["Face_Entity_Mapping"]
                # check if face_id exists in the face_entity_mapping
                    if "entity_id" in face_entity_mapping:
                        entity_id = face_entity_mapping["entity_id"]
                        if entity_id != "Default" and entity_id != "" and entity_id != None and entity_id != "string":
                            filered_entity_ids.append(entity_id)

            entity_id_count = Counter(filered_entity_ids)
            # create a list of dictionaries with entity_id and count
            entity_id_count_list = []
            for entity_id, count in entity_id_count.items():
                entity_id_count_list.append({"entity_id": entity_id, "count": count}
                                            )
            most_common_entity_id = None
            highest_count = 0
            if entity_id_count:
                most_common_entity_id, highest_count = entity_id_count.most_common(1)[0]
            most_common_entity_id = {"entity_id": most_common_entity_id, "count": highest_count}

            if most_common_entity_id!=None:
                logger.info(f"reading from docserver for the Most common entity is {most_common_entity_id}")
                #Read the entity name from the docserver
                try:
                    if most_common_entity_id['entity_id'] != "Default" and most_common_entity_id['entity_id'] != "" and most_common_entity_id['entity_id'] != None and most_common_entity_id['entity_id'] != "string":
                        entity_card = await get_entity_card(most_common_entity_id['entity_id'])
                        if 'textFullName' in entity_card:
                            most_common_entity_id['entity_name'] = entity_card['textFullName']
                        else:
                            logger.info(f"Entity name not found for entity id {most_common_entity_id['entity_id']}")
                    #most_common_entity_id['entity_name'] = entity_card['textFullName']
                except Exception as e:
                    logger.error(f"Error occurred while getting entity name from docserver: {e}")
            logger.info(f"Most common entity is {most_common_entity_id}")
            # average the distances which belongs to this entity
            distance_matched_list = []
            for simialr_image in results[key]:
                if simialr_image['entity_id'] == most_common_entity_id['entity_id']:
                    distance_matched_list.append(simialr_image['distance'])
            # just initializing the avg_distance with the first distance
            avg_distance = 0.5
            if len(results[key]) > 0:
                avg_distance = results[key][0]['distance']
            # calculate the average distance
            if len(distance_matched_list) > 0:
                avg_distance = round(np.mean(distance_matched_list), 2)
            face_dict = {"face_id": face_id_list[int(key)] , "face_url": face_url_list[int(key)], "similar_images": results[key], "entity_id_count": entity_id_count, "most_common_entity_id": most_common_entity_id, "avg_distance": avg_distance}
            search_results[face_id_list[int(key)]] = face_dict
            
        search_results_list = search_results
    return {"search_results": search_results_list}

@vector_search_router.post("/get-similar-images-by-face-relationship-format/" , tags=["Vector Search"] )
async def get_similar_images_by_face_relationship_format(payload: FaceDataPayload, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Given an image, identify the people in the image, return the similar images of the people from the vector Database
    """
    # logger.info(f" payload:{payload}")
    img_id = payload.imageId
    img_url = payload.imageUrl

    face_data = payload.faceData
    entity_id = payload.entity_id
    top_k = payload.top_k
    threshold = payload.threshold
    face_id_list = []
    face_url_list = []
    face_embeddings_list = []
    persons = payload.person_data
    # persons = file['persons']
    temp_persons = []
    if type(persons) == dict:
        for key, value in persons.items():
            if "embedding" in value:
                value['face_embedding'] = value['embedding']
                del value['embedding']
            temp_persons.append(value)
        persons = temp_persons


    for face in face_data:
        for person in persons:
            face_id = face.faceId
            if person['face_id'] == face_id:
                face_id_list.append(face.faceId)
                face_url_list.append(face.fileUrl)
                face_embeddings = person['face_embedding']
                face_embeddings_list.append(face_embeddings)
                break

    search_payload = {
                    "collection_name": VECTOR_DB_FACE_COLECTION,
                    "vector_field_name": "face_embeddings",
                    "query_vectors": face_embeddings_list,
                    "top_k": top_k,
                    #"search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : 0.7}},
                    "search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : threshold, "range_filter" : 0.999988}},  # range_filter is used to filter out the exact 100% match
                    "fields_to_return": ["face_id", "img_id","face_url", "img_url","entity_id","source_type"]
                }
    headers={"CF-Access-Client-Id": "084161eb89f7b63ed356bdb11cc415f3.access",
                                        "CF-Access-Client-Secret": "918ae96432f5ba9e69547900ea36a7618595ce83393cd4aa6cd5308edae76dbe"}
    async with httpx.AsyncClient() as client:
        response = await client.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
    # response = requests.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
    search_results = {}
    if response.status_code == 200:
        response_dict = json.loads(response.text)
        results = response_dict["results"]

        for key in results.keys():
            img_ids = [d['img_id'] for d in results[key]]
            entity_ids = [d['entity_id'] for d in results[key]]
            filered_entity_ids = []
            for img_id, entity_id in zip(img_ids, entity_ids):
                if entity_id == 'Default' or entity_id == '' or entity_id == None or entity_id == 'string':
                    logger.info(f"Entity id is not provided for image {img_id}, checking the docserver for entity id")
                    try:
                        #logger.info("!!!!!!!!!!")
                        file_info = await get_file(img_id)
                        #logger.info(f"Entity id is {file_info['_entityIds']}")
                        # cehck if _entityIds is present in the file_info
                        if '_entityIds' in file_info and file_info['_entityIds'] is not None and len(file_info['_entityIds']) > 0:
                            entity_id = file_info['_entityIds'][0]
                            logger.info(f"Entity id found in docserver: {entity_id}")
                            if entity_id != "Default" and entity_id != "" and entity_id != None and entity_id != "string":
                                filered_entity_ids.append(entity_id)
                            # replace the entity id in results
                            for result in results[key]:
                                if result['img_id'] == img_id:
                                    result['entity_id'] = entity_id
                    except Exception as e:
                        logger.error(f"Error occurred while getting entity id from docserver: {e}")

                else:
                    if entity_id != "Default" and entity_id != "" and entity_id != None and entity_id != "string":
                        filered_entity_ids.append(entity_id)

            entity_id_count = Counter(filered_entity_ids)
            # create a list of dictionaries with entity_id and count
            entity_id_count_list = []
            for entity_id, count in entity_id_count.items():
                entity_id_count_list.append({"entity_id": entity_id, "count": count}
                                            )
            most_common_entity_id = None
            highest_count = 0
            if entity_id_count:
                most_common_entity_id, highest_count = entity_id_count.most_common(1)[0]
            most_common_entity_id = {"entity_id": most_common_entity_id, "count": highest_count}
            if most_common_entity_id!=None:
                #Read the entity name from the docserver
                logger.info(f"reading from docserver for the Most common entity is {most_common_entity_id}")
                try:
                    if most_common_entity_id['entity_id'] != "Default" and most_common_entity_id['entity_id'] != "" and most_common_entity_id['entity_id'] != None and most_common_entity_id['entity_id'] != "string":
                        entity_card = await get_entity_card(most_common_entity_id['entity_id'])
                        if 'textFullName' in entity_card:
                            most_common_entity_id['entity_name'] = entity_card['textFullName']
                        else:
                            logger.info(f"Entity name not found for entity id {most_common_entity_id['entity_id']}")
                    #most_common_entity_id['entity_name'] = entity_card['textFullName']
                except Exception as e:
                    logger.error(f"Error occurred while getting entity name from docserver: {e}")
            face_dict = {"face_id": face_id_list[int(key)] , "face_url": face_url_list[int(key)], "similar_images": results[key], "entity_id_count": entity_id_count, "most_common_entity_id": most_common_entity_id}
            search_results[face_id_list[int(key)]] = face_dict
            
        search_results_list = search_results
        search_results_list = convert_similar_images_by_face_to_rels({"search_results": search_results_list})
    return search_results_list

@vector_search_router.post("/get-people-relationships/" , tags=["Vector Search"])
async def get_people_relationships(payload: FileDataItem, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    fileId = payload.fileId
    fileUrl = payload.fileUrl
    # search for persons in the file in the elastic
    try:
        file_info = await get_file_info(fileId,"persons,fileName")
        fileName = ""
        if file_info and "fileName" in file_info:
            fileName = file_info["fileName"]

        
        if file_info and "persons" in file_info:
            persons = file_info["persons"]
            if type(persons) == dict:  # for videos
                person_relationship_list = []
                for key, value in persons.items():
                    if "face_id" in value:
                        logger.info("----------------------------------------------")
                        logger.info(f"Fetching relationship for the file {fileId} and person {value['face_id']}")
                        face_id = value["face_id"]
                        face_url = value["fileUrl"]
                        face_detection_confidence = 1
                        if "face_detection_confidence" in value:
                            face_detection_confidence = value["face_detection_confidence"]
                            face_detection_confidence = round(face_detection_confidence, 2)
                        personName = ""
                        if "personName" in value:
                            personName = value["personName"]
                        # fetch the person name from face_crop
                        face_crop_info = await get_file_info(face_id, "personName")
                        if face_crop_info and "personName" in face_crop_info:
                            personName = face_crop_info["personName"]

                        if personName == "":
                            # search for the person name with vdb
                            # endpoint = "http://localhost:8000/MediaPropertiesRetrieval/get_person_name_vdb"
                            payload = {
                                "file_url": face_url,
                                "fileId": face_id,
                                "img_id": fileId
                            }
                            input_request_payload = InputRequestPayload(**payload)
                            try:
                                # async with httpx.AsyncClient() as client:
                                #     response = await client.post(endpoint, json=payload, timeout=30)
                                #     if response.status_code == 200:
                                #         response_json = response.json()
                                #         if response_json and "personName" in response_json:
                                #             personName = response_json["personName"]
                                response_json = await get_person_name_vdb(input_request_payload)
                                if response_json and "personName" in response_json:
                                    personName = response_json["personName"]
                            except httpx.RequestError as e:
                                logger.error(f"Error occurred while getting person name from vdb: {e}")
                        to = {
                            "textFullName": personName,
                            "nodeId": f"PERSON_{face_id}",
                            "entityType": "PERSONS",
                            "_id": face_id,
                            "fileUrl": face_url,

                        }
                        rel = {
                            "relationshipType": "PERSON",
                            "detectConfidence": face_detection_confidence,
                        }
                        from_ = {
                            "nodeId": f"IMAGE_{fileId}",
                            "entityType": "IMAGE",
                            "fileUrl": fileUrl,
                            "_id": fileId,
                            "textFullName": fileName if fileName!="" else f"IMAGE_{fileId}"
                        }
                        relationship = {
                            "from": from_,
                            "to": to,
                            "rel": rel
                        }
                        print(relationship)
                        person_relationship_list.append(relationship)
                return person_relationship_list

            if type(persons) == list: # for images
                person_relationship_list = []
                for person in persons:
                    if "face_id" in person:
                        logger.info("----------------------------------------------")
                        logger.info(f"Fetching relationship for the file {fileId} and person {person['face_id']}")
                        face_id = person["face_id"]
                        face_url = person["fileUrl"]
                        face_detection_confidence = person["face_detection_confidence"]
                        face_detection_confidence = round(face_detection_confidence, 2)
                        personName = ""
                        if "personName" in person:
                            personName = person["personName"]
                        if personName == "":
                            # search for the person name with vdb
                            # endpoint = "http://localhost:8000/MediaPropertiesRetrieval/get_person_name_vdb"
                            payload = {
                                "file_url": face_url,
                                "fileId": face_id,
                                "img_id": fileId
                            }
                            input_request_payload = InputRequestPayload(**payload)
                            try:
                                # async with httpx.AsyncClient() as client:
                                #     response = await client.post(endpoint, json=payload, timeout=30)
                                #     if response.status_code == 200:
                                #         response_json = response.json()
                                # if response_json and "person_name" in response_json:
                                #             personName = response_json["person_name"]

                                response_json = await get_person_name_vdb(input_request_payload)
                                if response_json and "personName" in response_json:
                                    personName = response_json["personName"]
                                        
                                        
                            except httpx.RequestError as e:
                                logger.error(f"Error occurred while getting person name from vdb: {e}")

                        person["personName"] = personName
                        to = {
                            "textFullName": personName,
                            "nodeId": f"PERSON_{face_id}",
                            "entityType": "PERSONS",
                            "_id": face_id,
                            "fileUrl": face_url,

                        }
                        rel = {
                            "relationshipType": "PERSON",
                            "detectConfidence": face_detection_confidence,
                        }
                        from_ = {
                            "nodeId": f"IMAGE_{fileId}",
                            "entityType": "IMAGE",
                            "fileUrl": fileUrl,
                            "_id": fileId,
                            "textFullName": fileName if fileName!="" else f"IMAGE_{fileId}"
                        }
                        relationship = {
                            "from": from_,
                            "to": to,
                            "rel": rel
                        }
                        print(relationship)
                        person_relationship_list.append(relationship)
                return person_relationship_list
    except Exception as e:
        logger.error(f"Error occurred while getting person info from the file: {e}")


@vector_search_router.post("/get-images-videos-from-target-or-case-relationships/" , tags=["Vector Search"])
async def get_images_videos_from_target_or_case_relationships(payload: TargetorCaseDataItem, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Given a target or case, return the images and videos from the target or case
    """
    targetId = payload.targetId
    caseId = payload.caseId
    if targetId == "Default" or targetId == "" or targetId == None or targetId == "string":
        pass
    else:
        # get the images and videos from the target
        pass



@vector_search_router.post("/fr-reinsert_vdb/" , tags=["Vector Search"])
async def fr_reinsert_vdb(payload: FrBackupPayload, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Reinsert the face embeddings to the vector db
    """
    # logger.info(f" payload:{payload}")
    backup_id = payload.BackupId
    collection_name = payload.CollectionName
    # get the face embeddings from the file server
    url = f"{DOC_SERVER_BASE_URL}/files/"
    params = {
        "query": f'NOT BackupId : {backup_id} AND _fileProcessorStatus.FacialRecognition: "processed" AND _exists_: persons.face_embedding',
        "fieldsToReturn": "persons,fileUrl,fileType",
        "itemsPerPage": 50
    }
    headers = {
        "accept": "application/json"
    }
    totalHits = None
    itemsPerPage = 50
    img_list = []
    while totalHits != 0:

        try:
            #response = requests.get(url, headers=headers, params=params)
            data = await fetch_unbackuped_files(backup_id,itemsPerPage)
            if "documents" not in data:
                raise HTTPException(status_code=400, detail="Unable to query docserver")
            else:
                logger.info(f"Successfully fetched {len(data['documents'])} items from the docserver")
            response_json = data["documents"]
            totalHits = data["totalHits"]
            logger.info(f"item remaining: {totalHits}")
            entities_to_insert = []
            for item in response_json:
                face_data = item['persons']
                img_id = item['_id']
                img_list.append(img_id)
                img_url = item['fileUrl']
                fileType = item['fileType']
                for face in face_data:
                    face_id = face['face_id']
                    face_embedding = face['face_embedding']
                    face_url = face['fileUrl']
                    if "blur_score" in face:
                        blur_score = face['blur_score']
                    else:
                        continue
                    if "alignment_score" in face:
                        alignment_score = face['alignment_score']
                    else:
                        continue
                    if "is_profile_image" in face:
                        is_profile_image = face['is_profile_image']
                    else:
                        is_profile_image = False
                        
                    if "num_faces_final" in face:
                        num_faces_final = face['num_faces_final']
                    else:
                        num_faces_final = 1

                    entity = {
                            "face_id": face_id,
                            "face_url": face_url,
                            "face_embeddings": face_embedding,
                            "img_id": img_id,
                            "img_url": img_url,
                            "entity_id": "Default",
                            "event_id": "Default",
                            "crawler": "Default",
                            "blur_score": blur_score,
                            "alignment_score": alignment_score,
                            "is_profile_image": is_profile_image,
                            "source_type": fileType,
                            "num_faces": num_faces_final
                        }
                    entities_to_insert.append(entity)

            # insert the face embeddings to the vector db
            insert_payload = {
                "entities": entities_to_insert,
                "collection_name": collection_name,
                "enable_flush": True
            }
            try:
                async with httpx.AsyncClient() as client:
                    vdb_response = await client.post(VECTOR_DB_INSERT_URL, json=insert_payload)
                # vdb_response = requests.post(VECTOR_DB_INSERT_URL, json=insert_payload)
                vdb_response.raise_for_status()
                vdb_response_json = vdb_response.json()
            except requests.RequestException as err:
                raise HTTPException(status_code=400, detail="Unable to insert entities to vector db")
            if vdb_response.status_code == 200:
                #update the backup id in the file server
                update_payload = {
                    "BackupId": backup_id
                }
                for item in response_json:
                    img_id = item['_id']
                    url = f"{DOC_SERVER_BASE_URL}/files/{img_id}"
                    try:
                        response = requests.patch(url, json=update_payload)
                        # logger.info(f"Successfully updated the backup id for the image {img_id} in the file server")
                        response.raise_for_status()
                    except requests.RequestException as err:
                        raise HTTPException(status_code=400, detail="Unable to update the backup id in the file server")
 
        except requests.RequestException as err:
            raise HTTPException(status_code=400, detail="Unable to query docserver")
        # delete the response_json
        del response_json
        del response  


        time.sleep(5)        
    print("Exiting while loop")

@vector_search_router.post("/get-entity-images/" , tags=["Vector Search"])
async def get_entity_images(payload: EntityCard, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    entity_id = payload.entity_id
    # Search the images which have faces from this entity card
    try:
        similar_images = await get_images_with_people_from_entity(entity_id, "_id,fileName,fileUrl")
        documents= []
        if similar_images and "documents" in similar_images:
            documents = similar_images["documents"]
        # convert this list of documents to the relationship format
        relationship_list = []
        for document in documents:
            img_id = document["_id"]
            img_url = document["fileUrl"]
            fileName = document["fileName"]
            to = {
                "nodeId": f"IMAGE_{img_id}",
                "entityType": "IMAGE",
                "fileUrl": await make_url_public(img_id, img_url),
                "_id": img_id,
                "textFullName": fileName
            }
            rel = {
                "relationshipType": "IMAGE",
            }
            from_ = {
                "nodeId": f"PERSON_{entity_id}",
                "entityType": "PERSONS",
                "_id": entity_id,
                "textFullName": entity_id
            }
            relationship = {
                "from": from_,
                "to": to,
                "rel": rel
            }
            relationship_list.append(relationship)
        return relationship_list


    except Exception as e:
        logger.error(f"Error occurred while getting images for the entity: {e}")

@vector_search_router.post("/common-image-relationship/" , tags=["Vector Search"])
async def common_image_relationship(payload: CommonImageRelationshipPayload, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    # Search the similar images for the given person ids, which have more than 1 person in the image
    persons_to_search = payload.persons
    top_k = payload.top_k
    person_data = {}
    face_id_list = []
    for item in persons_to_search:
        face_id = item.faceId
        file_id = item.fileId
        face_id_list.append(face_id)
        # get the face_embedding from the file server
        file_info = await get_file_info(file_id, "persons")
        persons = file_info["persons"]
        temp_persons = []
        if type(persons) == dict:
            for key, value in persons.items():
                if "embedding" in value:
                    value['face_embedding'] = value['embedding']
                    del value['embedding']
                temp_persons.append(value)
            persons = temp_persons
        results = None
        for person in persons:
            if person['face_id'] == face_id:
                face_embedding = [person['face_embedding']]
                search_payload = {
                    "collection_name": VECTOR_DB_FACE_COLECTION,
                    "vector_field_name": "face_embeddings",
                    "query_vectors": face_embedding,
                    "top_k": top_k,
                    "search_params": {"metric_type": "IP", "params": {"nprobe": 10, "radius" : 0.5, "range_filter" : 0.999988}},
                    "fields_to_return": ["img_id"]
                }
                headers={"CF-Access-Client-Id": "084161eb89f7b63ed356bdb11cc415f3.access",
                                        "CF-Access-Client-Secret": "918ae96432f5ba9e69547900ea36a7618595ce83393cd4aa6cd5308edae76dbe"}
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
                    # response = requests.post(VECTOR_DB_SEARCH_URL, headers=headers, data=json.dumps(search_payload))
                    response.raise_for_status()
                    response_json = response.json()
                    results = response_json["results"]
                except requests.RequestException as err:
                    raise HTTPException(status_code=400, detail="Unable to query vector db")
                break
        if results:
            person_data_item = {
                "imgs": results["0"]
            }
            person_data[face_id] = person_data_item

    common_images = find_common_img_id(person_data)
    
    #convert the common images to the relationship format
    relationship_list = []
    for face_id in face_id_list:
        for img_id in common_images:
            to = {
                "nodeId": f"IMAGE_{img_id}",
                "entityType": "IMAGE",
                "_id": img_id
            }
            rel = {
                "relationshipType": "IMAGE",
            }
            from_ = {
                "nodeId": f"PERSON_{face_id}",
                "entityType": "PERSONS",
                "_id": face_id
            }
            relationship = {
                "from": from_,
                "to": to,
                "rel": rel
            }
            relationship_list.append(relationship)
    return {"common_images": common_images, "common_images_relationship": relationship_list}

@vector_search_router.post("/construct-vdb-payload-from-persons" , tags=["Vector Search"])
async def construct_vdb_payload_from_file_id(payload: FileDataItem, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    """
    Construct the vdb payload from the persons in the file
    """
    file_info = await get_file_info(payload.fileId, "persons,num_faces_final,fileType")
    if file_info and "num_faces_final" in file_info:
        num_faces_final = file_info["num_faces_final"]
    if file_info and "fileType" in file_info:
        fileType = file_info["fileType"]

    if file_info and "persons" in file_info:
        persons = file_info["persons"]
        temp_persons = []
        if type(persons) == list: # for images
            for person in persons:
                vdb_entity = {}
                vdb_entity["face_id"] = person["face_id"]
                vdb_entity["face_url"] = person["fileUrl"]
                vdb_entity["face_embeddings"] = person["face_embedding"]
                vdb_entity["img_id"] = payload.fileId
                vdb_entity["img_url"] = await make_url_public(payload.fileId, payload.fileUrl)
                vdb_entity["entity_id"] = "Default"
                vdb_entity["event_id"] = "Default"
                vdb_entity["crawler"] = "Default"
                vdb_entity["blur_score"] = person["blur_score"]
                vdb_entity["alignment_score"] = person["alignment_score"]
                vdb_entity["is_profile_image"] = person["is_profile_image"]
                vdb_entity["num_faces"] = num_faces_final
                vdb_entity["source_type"] = fileType
                temp_persons.append(vdb_entity)

        return {"entities": temp_persons, "collection_name": VECTOR_DB_FACE_COLECTION, "enable_flush": True}


@vector_search_router.post("/ImageSearchFromImageFileId/" , tags=["Vector Search"])
async def image_search_from_image_file_id(payload: FileDataItem, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    # Check if image has any persons in it. If yes,
    # 1. If the num of persons is 1, then search for similar images of all the persons

    #if there are no people in the image, then search from clip_embeddings similar images
    try:
        similar_images = []
        persons = []
        file_info = await get_file_info(payload.fileId, "persons,num_faces_final")
        if file_info and "num_faces_final" in file_info:
            num_faces_final = file_info["num_faces_final"]
        if file_info and "persons" in file_info:
            persons = file_info["persons"]
        if len(persons) == 0:
            # search for similar images from clip_embeddings
            logger.info(f"Searching for similar images from clip embeddings for the image {payload.fileId} because there are no persons in the image")
            clip_response = await clip_search_image_from_file_id(payload.fileId)
            if clip_response and "results" in clip_response:
                print(f'Clip response: {clip_response["results"]}')
                similar_images = clip_response["results"]['0']
                # similar_images = [i["img_id"] for i in similar_images]
                #convert to relationship format
                relationship_list = []
                
                # create image entity if doesn't exist for the image
                await create_image_entitycard_if_doesnt_exist(payload.fileId)

                for image in similar_images:
                    
                    # create image entity if doesn't exist for the image
                    await create_image_entitycard_if_doesnt_exist(image["img_id"])
                    to = {
                        "nodeId": f"IMAGE_{image['img_id']}",
                        "entityType": "IMAGE",
                        "_id": image["img_id"]
                    }
                    rel = {
                        "relationshipType": "IMAGE",
                        "distance": round(image["distance"], 2)
                    }
                    from_ = {
                        "nodeId": f"IMAGE_{payload.fileId}",
                        "entityType": "IMAGE",
                        "_id": payload.fileId
                    }
                    relationship = {
                        "from": from_,
                        "to": to,
                        "rel": rel
                    }
                    relationship_list.append(relationship)
                return relationship_list

        if len(persons) > 0:
            # get similar image relationships for all the persons
            logger.info(f"Searching for similar images for all the persons in the image {payload.fileId} using FR")
            similar_images_relationships = await get_similar_images_relationships_from_image_id(payload.fileId)

            return similar_images_relationships
        
    except Exception as e:
        logger.error(f"Error occurred while searching for similar images from image file id: {e}")