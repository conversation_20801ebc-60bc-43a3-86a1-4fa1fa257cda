import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx
from fastapi import APIRouter, Body, HTTPException, Request
from httpx import AsyncClient
from pydantic import BaseModel

from .config import (
    CCE_PUBLIC_HEADER,
    DEV_PUBLIC_HEADER,
    USE_AZURE_SERVICE_BUS,
    VECTOR_DB_INSERT_URL,
)
from .dependencies import *
from .receiver_utils import *
from .relationships import *
from .utils import *
from .ip_utils import get_client_ip

DEBUG_MODE = os.getenv('DEBUG_MODE', False)
logging.basicConfig(
    datefmt='%m/%d/%y | %I:%M:%S %p',
    format='%(asctime)s| %(threadName)s| %(levelname)s| %(lineno)3s| %(filename)s: %(message)s',
    handlers=[logging.StreamHandler()]
)
logging.info('logger initialized')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)

receive_router = APIRouter()


@receive_router.post("/store_data", tags=["Receive"])
async def store_data(payload: dict, req: Request):
    logging.info(f"IP INFO: {get_client_ip(req)}")
    # Steps:
    # 2. verify if vdb_payload in the payload
    # 3. Save the face embeddings to the vector db
    # 4. Remove the vdb_payload from the payload
    # 5. Download the face_urls and upload to the azure blob/ s3
    # 6. Replace the face_urls with the new urls
    # 7. Save the payload to the elastic search
    # 8. Check if the relationship-data is present in the payload
    # 9. Save the relationship-data to Neo4j
    # 10. Return the response
    serviceName = payload['serviceName']
    logger.info(f"Service Name: {serviceName}")
    data = [payload['data']]
    env = payload['env']
    logger.info(f"No. of items in the receive payload: {len(data)}")
    for item in data:
        # serviceName = item['serviceName']
        apiTag = item['apiTag']
        status = item['status']
        fileId = item['fileId']
        results = item['results']
        apiBody = item['apiBody']
        if type(apiBody) == str:
            apiBody = json.loads(apiBody)
        metaData = apiBody['metaData']
        redaction = False
        if 'redaction' in apiBody:
            redaction = apiBody['redaction']
        is_profile_image = False
        if metaData and 'profile_image' in metaData:
            profile_image = metaData['profile_image']
            if 'profile_image' in profile_image:
                is_profile_image = True
        original_profile_image_url = None
        if metaData and "original_profile_image_url" in metaData:
            original_profile_image_url = metaData["original_profile_image_url"]
        
        reupload = RE_UPLOAD_FILES

        logger.info(f"Processing the payload for the fileId: {fileId} and apiTag: {apiTag}")
        # update the status of the file, _receivedmsgstatus
        current_datetime = datetime.utcnow().isoformat(timespec='microseconds') + "Z"
        patch_payload = {
            "_receivedmsgstatus": f" AI gateway Status: {status}, message received from {serviceName}",
            "_messagereceivingTimestamps": {
                f"{serviceName}": current_datetime
            },
            "_fileProcessorStatus": {
                serviceName: status
            }
        }
        # get the existing file status
        latest_file_info = await get_file_info(fileId, fields_to_return='_receivedmsgstatus')
        if latest_file_info and '_receivedmsgstatus' in latest_file_info:
            # append the new status to the existing status
            patch_payload['_receivedmsgstatus'] = f"{latest_file_info['_receivedmsgstatus']}, {patch_payload['_receivedmsgstatus']}"
        # update the file with the new status
        try:
            res = await update_file(fileId, patch_payload)
        except Exception as e:
            logger.error(f"Failed to save the status to the elastic search with error for the fileId: {fileId}: {e}")

        # process profile images
        if 'FacialRecognition' == serviceName and is_profile_image:
            try:
                if apiTag == 'GetFacialRecognitionByImage':
                    if 'persons' in results:
                        if reupload:
                            for person in results["persons"]:
                                person['fileUrl'] = CCE_PUBLIC_HEADER + person['fileUrl']
                                # upload the file to the azure blob
                                res = await upload_file_using_url(person['fileUrl'],parent_doc_id=fileId)
                                if res.status_code == 200:
                                    data = res.json()
                                    new_file_fileUrl = data['fileUrl']
                                    new_file_id = data['_id']
                                    old_file_id = person['face_id']
                                    old_file_fileUrl = person['fileUrl']
                                    person['fileUrl'] = new_file_fileUrl
                                    person['face_id'] = new_file_id
                                    logger.info(f"Reupload successful for the file: {fileId} and face_id: {new_file_id}")
                                    # update the face_id in the vdb_payload
                                    if 'vdb_payload' in results:
                                        for entity in results['vdb_payload']['entities']:
                                            if entity["face_id"] == old_file_id:
                                                entity["face_id"] = new_file_id
                                                entity["face_url"] = new_file_fileUrl
                                                break

                    # skip the relationship creation for the profile images
                    # Save to vdb_payload
                    if not redaction:  
                        if 'vdb_payload' in results:
                            if len(results['vdb_payload']['entities'])>0:
                                logger.info(f"vdb_payload found for {fileId} and {apiTag}")

                                # update the img_url to the original one
                                for entity in results['vdb_payload']['entities']:
                                    if original_profile_image_url:
                                        entity['img_url'] = original_profile_image_url
                                    else:
                                        entity['img_url'] = entity['face_url']
                                # Save the face embeddings to the vector db
                                vector_db_url  = VECTOR_DB_INSERT_URL
                                try:
                                    async with AsyncClient() as client:
                                        response = await client.post(vector_db_url, json=results['vdb_payload'], timeout=30)
                                    if response.status_code == 200:
                                        logger.info(f"Vector DB update successful for {fileId} and {apiTag}")
                                except Exception as e:
                                    logger.error(f"Vector DB update failed with status {response.status_code}")
                                    return {"status": "failed", "message": "Failed to save the face embeddings to the vector db"}
                                
                                # # Remove the entities_to_insert from the payload
                                # if 'vdb_payload' in results:
                                #     del results['vdb_payload']
                            else:
                                logger.info(f"No entities to insert for {fileId} and {apiTag}")
                    # Save the payload to the entity_card document
                    socialNetwork_source = apiBody['metaData']['source']
                    entity_id = apiBody['metaData']['entity_id']
                    crawler_field_in_elastic = ""
                    for snetworks in social_networks_profle_image_mapping.keys():
                        if snetworks == socialNetwork_source:
                            crawler_field_in_elastic = socialNetwork_source
                    if results and 'vdb_payload' in results:
                        update_data = {
                            crawler_field_in_elastic:{
                                "facialRecognition": "processed",
                                "facialRecognitionResults": results['vdb_payload']
                            }}
                    else:
                        update_data = {
                            crawler_field_in_elastic:{
                                "facialRecognition": "processed",
                                "facialRecognitionResults": results
                            }}
                    # update enttiy card
                    logger.info(f"Update the entity card with the analysed info for entityId: {entity_id} and data: {update_data}")
                    try:
                        if crawler_field_in_elastic in update_data:
                            entities = update_data[crawler_field_in_elastic].get("facialRecognitionResults", {}).get("entities", [])
                            for entity in entities:
                                entity.pop("face_embeddings", None)

                        res = await update_entity_card(entity_id, update_data)
                        logger.info(f'Updated entity card for {apiTag} and {fileId}: {res}')
                    except Exception as e:
                        logger.error(f"Failed to save the facial recognition payload to the elastic search with error for the fileId: {fileId}: {e}")
                        return {"status": "failed", "message": "Failed to save the facial recognition payload to the elastic search"}
            except Exception as e:
                logger.error(f"Failed to process the profile for entityId: {entity_id} and error: {e}")

        # process non-profile images
        elif 'FacialRecognition' == serviceName and not is_profile_image:
            if apiTag == 'GetFacialRecognitionByImage':
                if 'persons' in results:
                    if reupload:
                        for person in results["persons"]:
                            person['fileUrl'] = CCE_PUBLIC_HEADER + person['fileUrl']
                            # upload the file to the azure blob
                            res = await upload_file_using_url(person['fileUrl'],parent_doc_id=fileId)
                            if res.status_code == 200:
                                data = res.json()
                                new_file_fileUrl = data['fileUrl']
                                new_file_id = data['_id']
                                old_file_id = person['face_id']
                                old_file_fileUrl = person['fileUrl']
                                person['fileUrl'] = new_file_fileUrl
                                person['face_id'] = new_file_id
                                logger.info(f"Reupload successful for the file: {fileId} and new face_id: {new_file_id} and new fileUrl: {new_file_fileUrl}")
                                # update the face_id in the vdb_payload
                                if 'vdb_payload' in results:
                                    for entity in results['vdb_payload']['entities']:
                                        if entity["face_id"] == old_file_id:
                                            entity["face_id"] = new_file_id
                                            entity["face_url"] = new_file_fileUrl
                                            break
                img_info = await get_file_info(fileId, fields_to_return='fileUrl,_eventIds,_entityIds,persons,Face_Entity_Mapping,azure_analytics')

                #----------------------------------  RELATIONSHIP CODE -------------------------------------       
                try:     
                    if not redaction:  
                        # create relationships between entities
                        # create image entity card       
                        # Create Entity/Case->Image relationship 
                        # create the source entity relationships
                        image_entity = await create_entity_relationship(fileId, img_info)


                        person_entity_cards = []

                        # create relationshipds for faces, either link to existing entity or create a new entity
                        results = await create_persons_relationships(fileId, results, image_entity, apiTag, img_info)

                except Exception as e:
                    logger.error(f"Failed to create Image->Person Relationship: {e}")
                #----------------------------------  RELATIONSHIP CODE END-------------------------------------
                if 'vdb_payload' in results:
                    if len(results['vdb_payload']['entities'])>0:
                        logger.info(f"vdb_payload found for {fileId} and {apiTag}")

                        # update the img_url to the original one
                        for entity in results['vdb_payload']['entities']:
                            entity['img_url'] = img_info['fileUrl']


                        # logger.info(f"vdb_payload: {results['vdb_payload']}")
                        # Save the face embeddings to the vector db
                        vector_db_url  = VECTOR_DB_INSERT_URL
                        try:
                            async with AsyncClient() as client:
                                response = await client.post(vector_db_url, json=results['vdb_payload'], timeout=30)
                            if response.status_code == 200:
                                logger.info(f"Vector DB update successful for {fileId} and {apiTag}")
                        except Exception as e:
                            logger.error(f"Vector DB update failed with status {response.status_code}")
                            return {"status": "failed", "message": "Failed to save the face embeddings to the vector db"}
                        
                        # Remove the entities_to_insert from the payload
                        if 'vdb_payload' in results:
                            del results['vdb_payload']
                    else:
                        logger.info(f"No entities to insert for {fileId} and {apiTag}")
                # Save the payload to the elastic search
                try:
                    # change the key name persons.face_landmarks to persons._face_landmarks due to the issue in Frozen Diamond environment when saving to elastic, because mapping is DEnse vector while data is of float
                    # for person in results['persons']:
                    #     if 'face_landmarks' in person:
                    #         person['_face_landmarks'] = person['face_landmarks']
                    #         del person['face_landmarks']
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the facial recognition results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }
                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the facial recognition payload to the elastic search with error for the fileId: {fileId}: {e}")
                except Exception as e:
                    logger.error(f"Failed to save the facial recognition payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the facial recognition payload to the elastic search"}
            elif apiTag == 'GetFacialRecognitionByVideo':
                video_info = await get_file_info(fileId, fields_to_return='fileUrl,_eventIds,_entityIds,persons,Face_Entity_Mapping,azure_analytics')
                
                if reupload:
                    persons = results['persons']
                    results['persons'] = await reupload_faces_video(persons, fileId)



                #----------------------------------  RELATIONSHIP CODE -------------------------------------
                # create video entity card
                video_info = await get_file_info(fileId, fields_to_return='fileUrl,_eventIds,_entityIds,persons,Face_Entity_Mapping,azure_analytics')

                if not redaction:
                    # create the source entity relationships
                    video_entity = await create_entity_relationship_video(fileId, video_info)

                    # create relationshipds for faces, either link to existing entity or create a new entity
                    results = await create_persons_relationships(fileId, results, video_entity, apiTag, video_info)
                #----------------------------------  RELATIONSHIP CODE END-------------------------------------
                if 'vdb_payload' in results:
                    # update the img_url to the original one
                    for entity in results['vdb_payload']['entities']:
                        entity['img_url'] = video_info['fileUrl']
                    results = await process_vdb_payload_video(results, fileId, apiTag, redaction)

                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the facial recognition results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }
                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the facial recognition payload to the elastic search with error for the fileId: {fileId}: {e}")
                except Exception as e:
                    logger.error(f"Failed to save the facial recognition video payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the facial recognition payload to the elastic search"}

                    
        elif 'ObjectDetection' == serviceName:
            if apiTag == 'DetectObjectsInImage':
                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the object detection results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }
                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the object detection payload to the elastic search with error for the fileId: {fileId}: {e}")
                except Exception as e:
                    logger.error(f"Failed to save the object detection payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the object detection payload to the elastic search"}
            elif apiTag == 'GetObjectDetectionByVideo':
                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the object detection results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }
                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the object detection payload to the elastic search with error for the fileId: {fileId}: {e}")
                except Exception as e:
                    logger.error(f"Failed to save the object detection payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the object detection payload to the elastic search"}
                
        elif serviceName == 'KeyFrames':
            if apiTag == 'GetKeyFramesFromVideoURL':
                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')
                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the keyframes results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }
                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the keyframes payload to the elastic search with error for the fileId: {fileId}: {e}")
                except Exception as e:
                    logger.error(f"Failed to save the key frames payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the key frames payload to the elastic search"}
        elif serviceName == 'TranscriptionService':
            if apiTag == 'getTranscriptionsFromVideo' or apiTag == 'getTranscriptionsFromAudio':
                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')
                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the transcription results
                            try:
                                if results and '_fileProcessorStatus' in results:
                                    patch_payload = {
                                        '_docProcessorStatus': results['_fileProcessorStatus']
                                    }                               

                                res = await update_document(main_artifact_id, "Artifact", patch_payload)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the transcription payload to the elastic search with error for the fileId: {fileId}: {e}")

                except Exception as e:
                    logger.error(f"Failed to save the transcription payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the transcription payload to the elastic search"}
        elif serviceName == 'OcrService':
            if apiTag == 'getOcrTextFromImage' or apiTag == 'getOcrTextFromVideo':
                # Save the payload to the elastic search
                try:
                    res = await update_file(fileId, results)
                    logger.info(f'Updated file for {apiTag} and {fileId}: {res}')
                    # Also need to update the Artifact if the file belongs to the artifact
                    locate_res = await locate_file(fileId)

                    if locate_res and 'Artifact_Results' in locate_res:
                        Artifact_Results = locate_res['Artifact_Results']
                        if 'documents' in Artifact_Results:
                            documents = Artifact_Results['documents']
                            main_artifact_id = documents[0]['_id']

                            # Update the main artifact with the ocr results
                            try:
                                if results and 'text' in results:
                                    results['Text'] = results['text']
                                    del results['text']
                                elif results and 'combined_text' in results:# This is for videos
                                    results['Text'] = results['combined_text']
                                    del results['combined_text']
                                if results and '_fileProcessorStatus' in results:
                                    results['_docProcessorStatus'] = results['_fileProcessorStatus']
                                    del results['_fileProcessorStatus']                                  

                                res = await update_document(main_artifact_id, "Artifact", results)
                                logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                            except Exception as e:
                                logger.error(f"Failed to save the ocr payload to the elastic search with error for the fileId: {fileId}: {e}")                                    

                except Exception as e:
                    logger.error(f"Failed to save the ocr payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the ocr payload to the elastic search"}
        elif serviceName == 'NudeDetection':    
            if apiTag == 'getNudeDetectionsFromImage' or apiTag == 'getNudeDetFromVideo':
                # Save the payload to the elastic search
                try:
                    response = results
                    nude_detections = []
                    if "nude_detections" in response:
                        nude_detections = response["nude_detections"]
                    is_pornographic = False
                    if nude_detections == "No detections":
                        is_pornographic = False
                        nude_detections = []
                        logger.info(f"No nude detections found for {fileId} and {apiTag}")
                    elif len(nude_detections) > 0:
                        is_pornographic = True

                    if is_pornographic:
                        patch_payload = {
                            "_fileProcessorStatus": {
                                "pornography_analysed": "processed"
                            },
                            "NudeDetections": nude_detections,
                            "ForensicTags": ['Pornography']
                        }
                        # Save the payload to the elastic search
                        res = await update_file(fileId, patch_payload)
                        logger.info(f'Updated file for {apiTag} and {fileId}: {res}')
                    else:
                        patch_payload = {
                            "_fileProcessorStatus": {
                                "pornography_analysed": "processed"
                            },
                            "NudeDetections": nude_detections,
                        }
                        # Save the payload to the elastic search
                        res = await update_file(fileId, patch_payload)
                        logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                except Exception as e:
                    logger.error(f"Failed to save the nude detection payload to the elastic search with error for the fileId: {fileId}: {e}")
                    return {"status": "failed", "message": "Failed to save the nude detection payload to the elastic search"}
        else:
            logger.info(f" Other type Payload received: {item}")
            # save the payload to the elastic search
            try:
                res = await update_file(fileId, results)
                logger.info(f'Updated file for {apiTag} and {fileId}: {res}')

                # Also need to update the Artifact if the file belongs to the artifact
                locate_res = await locate_file(fileId)

                if locate_res and 'Artifact_Results' in locate_res:
                    Artifact_Results = locate_res['Artifact_Results']
                    if 'documents' in Artifact_Results:
                        documents = Artifact_Results['documents']
                        main_artifact_id = documents[0]['_id']

                        # Update the main artifact with the results
                        try:
                            if results and '_fileProcessorStatus' in results:
                                patch_payload = {
                                    '_docProcessorStatus': results['_fileProcessorStatus']
                                }
                            res = await update_document(main_artifact_id, "Artifact", patch_payload)
                            logger.info(f'Updated main artifact for {apiTag} and {fileId}: {res}')
                        except Exception as e:
                            logger.error(f"Failed to save the payload to the elastic search with error for the fileId: {fileId}: {e}")
            except Exception as e:
                logger.error(f"Failed to save the payload to the elastic search with error for the fileId: {fileId}: {e}")
                return {"status": "failed", "message": "Failed to save the payload to the elastic search"}
