#!/bin/bash

set -x # prints trace of commands & arguments after they're expanded and before execution
set -eo pipefail # script exit immediately if any command fails and exits with non-zero status 

export DEBIAN_FRONTEND=noninteractive

# # Install Python dependencies
# pip install --upgrade pip setuptools wheel
# pip install cython
# pip install cmake
# pip install onnxruntime-gpu
# pip install -U insightface

python model_setup.py

# Run main.py using gunicorn with custom access log format
gunicorn -k uvicorn.workers.UvicornWorker -w 2 --preload -b 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile - \
  --log-level info \
  --access-logformat '%(h)s:%(p)s - "%(r)s" %(s)s' \
  main:app &

# Run listener.py
python listener.py