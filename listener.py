import asyncio
import json
import logging
import time
from typing import Any, Dict, List, Optional

import httpx
import requests
from azure.servicebus import ServiceBusClient
from pydantic import BaseModel
from tenacity import (
    RetryError,
    before_sleep_log,
    retry,
    retry_if_exception_type,
    retry_if_result,
    stop_after_attempt,
    wait_exponential,
)

from app.config import *
from app.receive_router import store_data
from S2t_ServiceBus_Client import S2tServiceBusClient

DEBUG_MODE = os.getenv('DEBUG_MODE', False)
logging.basicConfig(
    datefmt='%m/%d/%y | %I:%M:%S %p',
    format='%(asctime)s| %(threadName)s| %(levelname)s| %(lineno)3s| %(filename)s: %(message)s',
    handlers=[logging.StreamHandler()]
)
logging.info('logger initialized')
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if DEBUG_MODE else logging.INFO)

def is_bad_response(response):
    return response is None or response.status_code != 200

@retry(
    retry=(retry_if_exception_type(Exception) | retry_if_result(is_bad_response)),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
    reraise=True,
    before_sleep=before_sleep_log(logger, logging.WARNING)
)
async def _make_request_with_retry(url, data, headers):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=data, headers=headers)
            return response
        except Exception as e:
            logger.error(f"Request error: {e}")
            raise

async def fetch_ai_manager_data(ai_manager_get_results, data, headers=None):
    try:
        response = await _make_request_with_retry(ai_manager_get_results, data, headers)
        if not response or response.status_code != 200:
            logger.error(f"Failed to fetch the analysis: {response.text}")
            return None
        return response.json()
    except RetryError as e:
        logger.error(f"Retry failed after multiple attempts: {e}")
        return None


async def listen_to_service_bus():

    if USE_AZURE_SERVICE_BUS:
        logger.info("Using Azure Service Bus")
        logger.info(f" Connection String: {AZURE_SERVICE_BUS_CONNECTION_STRING}")
        logger.info(f" Topic Name: {AZURE_SERVICE_BUS_TOPIC_NAME}")
        logger.info(f" Service Name: {AZURE_SERVICE_BUS_SERVICE_NAME}")
        logger.info(f" ENV Default System ID: {DEFAULT_SYSTEM_ID}")

        client = S2tServiceBusClient(AZURE_SERVICE_BUS_CONNECTION_STRING, DEFAULT_SYSTEM_ID, AZURE_SERVICE_BUS_TOPIC_NAME , service_name=AZURE_SERVICE_BUS_SERVICE_NAME)
        client.create_subscription()
        while True:
            try:
                msgs = client.receive_messages(DEFAULT_SYSTEM_ID)
                for msg in msgs:
                    try: 
                        message_body_bytes = b"".join(msg.body)
                        message_body_str = message_body_bytes.decode('utf-8')
                        # Load the JSON string into a Python dictionary
                        message_data = json.loads(json.loads(message_body_str))
                        fileId = message_data['fileId']
                        # check if env is present in the message
                        logger.info(f"Processing the message: {message_data}")
                        env = None
                        if 'env' in message_data:
                            env = message_data['env']
                        if 'serviceName' in message_data:
                            serviceName = message_data['serviceName']
                        if 'taskId' in message_data:
                            taskId = message_data['taskId']
                        else: 
                            logger.error(f"TaskId not found in the message: {message_data}")
                        if env == DEFAULT_SYSTEM_ID:
                            logger.info(f"Message received: {message_body_str}")
                            logger.info(f"Service Namefor the file: {fileId} is: {serviceName}")
                            logger.info(f"Calling ai-manager to fetch the analysis for fileId: {fileId} and env: {env}")
                            # call the ai-manager service to fetch the analysis
                            ai_manager_get_results = AI_GATEWAY_RECEIVE_DATA_URL
                            data = {
                                "taskId": taskId,
                            }
                            # get request to the ai-manager service
                            headers = None
                            if "dev" in DEFAULT_SYSTEM_ID or "DEV" in DEFAULT_SYSTEM_ID or USE_LOCAL_ENV or USE_INTERNAL_AI_GATEWAY:# or "stg" in DEFAULT_SYSTEM_ID or "STG" in DEFAULT_SYSTEM_ID:
                                ai_manager_get_results = AI_GATEWAY_RECEIVE_DATA_URL
                            else:
                                ai_manager_get_results = PROXY_AI_GATEWAY_RECEIVE_DATA_URL
                                headers = {
                                    "Ocp-Apim-Subscription-Key": OCP_APIM_SUBSCRIPTION_KEY,
                                    "Cache-Control": CACHE_CONTROL
                                }
                            response_data = await fetch_ai_manager_data(ai_manager_get_results, data, headers=headers)

                            try:
                                # response = requests.get(ai_manager_get_results, params=data)
                                if response_data is None:
                                    logger.error(f"Failed to fetch the analysis for fileId: {fileId} and env: {env}")
                                    logger.error(f"Response: {response.text}")
                                else:
                                    # response_data = response.json()
                                    # Add the serviceName to the response_data
                                    store_payload = {
                                        "serviceName": serviceName,
                                        "data": response_data,
                                        "env": env,
                                    }
                                    logger.info(f"Store Payload: {store_payload}")
                                    # # call the store_data endpoint of the receive service
                                    # store_data_url = "http://localhost:8000/Receive/store_data"
                                    # try:
                                    #     logger.info(f"Sending the analysis to the store_data endpoint of the receive service for fileId: {fileId} and env: {env}")
                                    #     response = requests.post(store_data_url, json=store_payload)
                                    #     logger.info(response.status_code)
                                    # except Exception as e:
                                    #     logger.error(f"Failed to call the store_data endpoint of the receive service for fileId: {fileId} and env: {env}")
                                    #     logger.error(e)

                                    # instead of calling the store_data endpoint of the receive service, we can directly call the store_data function
                                    response = await store_data(store_payload, {})
                                    logger.info(f"!!! Store data is successful for fileId: {fileId} and env: {env}")

                            except Exception as e:
                                logger.error(f"Failed to fetch the analysis for fileId: {fileId} and env: {env} with message: {message_data} and exception: {e}")
                                logger.error(e)
                    except Exception as e:
                        logger.error(f"Failed to process the message: {msg} with exception: {e}")
                        logger.error(e)
            except Exception as e:
                logger.error(f"Failed to receive messages from the service bus with exception: {e}")
                logger.error(e)
            # logger.info("---------------------------")
            time.sleep(0.5)

if __name__ == "__main__":
    logger.info(f"Using Azure Service Bus: {USE_AZURE_SERVICE_BUS}")
    if USE_AZURE_SERVICE_BUS:
        asyncio.run(listen_to_service_bus())
