## [1.13.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.13.0...1.13.1) (2025-05-30)


### Bug Fixes

* make request optional ([60d49ba](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/60d49bac4233a7476d3ec68e3f6d7ed992db90cf))

# [1.13.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.12.1...1.13.0) (2025-05-29)


### Features

* double priority for non-default custom environments ([ee6689e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ee6689ec57d31b1ce0b19e28ed5709fc3d6f733d))

## [1.12.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.12.0...1.12.1) (2025-05-27)


### Bug Fixes

* correctly remove face_embeddings from each entity ([20ca514](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/20ca514758b75a7944584f1bbbe06d19e7d7e994))

# [1.12.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.11.1...1.12.0) (2025-05-26)


### Bug Fixes

* don't save face_embeddings in entity card ([99b46e4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/99b46e4a6b6624b8605bcb2dae5f3f90bb3aec5b))


### Features

* **logging:** configure gunicorn to display IP and endpoint in access logs ([d22cfe1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d22cfe1f62c56748d8ac8607fa6080650f484b12))

## [1.11.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.11.0...1.11.1) (2025-05-14)


### Bug Fixes

* ensure response is not None before checking for status_code ([b201ca4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b201ca414da64e1c722ca44e351c90f58e53e8e5))

# [1.11.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.10.1...1.11.0) (2025-05-06)


### Bug Fixes

* retry for exceptions as well ([ce86321](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ce863211a5d686d6b185a06c022146c2a1ddd19c))


### Features

* before_sleep_log ([0daf31f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0daf31f66930e7a05334f1627ed367daa4480562))

## [1.10.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.10.0...1.10.1) (2025-05-06)


### Bug Fixes

* retry failed AI Gateway requests ([5c16402](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5c16402be2a9add038ddbb6433f5a3897ace8cc2))

# [1.10.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.5...1.10.0) (2025-05-06)


### Features

* retry ai gateway request ([a7b945d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a7b945d32d7356fb5867f4906a83ebfa620b64b0))

## [1.9.5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.4...1.9.5) (2025-05-05)


### Bug Fixes

* Generate a presigned url instead of docserver downloadable url ([0c1159d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0c1159ddfa8431129fca6dba267c4b384dadd9c4))

## [1.9.4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.3...1.9.4) (2025-05-05)


### Bug Fixes

* make file_url optional (we can generate it if not provided) ([468a0e2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/468a0e2820a7e46763f88e0053eb00bcc910ba50))

## [1.9.3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.2...1.9.3) (2025-05-05)


### Bug Fixes

* include service in _fileProcessorStatus ([7908483](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/79084837b82e65d8c3538f1974ba3b73ae1d1979))

## [1.9.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.1...1.9.2) (2025-05-02)


### Bug Fixes

* create a downloadable docserver url if file_url is not provided ([33ba473](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/33ba4730055ad15ebc86a1c21c468ac794859e75))

## [1.9.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.9.0...1.9.1) (2025-04-29)


### Bug Fixes

* pass empty request in listener.py ([cf48573](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cf48573f027b44a031bcad82ac8860a7d50a27ed))

# [1.9.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.8.2...1.9.0) (2025-04-29)


### Features

* add ip logging ([655e36a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/655e36aea368e45f506e742419d0121a2e8e173c))
* improve ip logging with method and path specified ([a29545f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a29545f8bf464b97733084de3a8cc107310c4229))

## [1.8.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.8.1...1.8.2) (2025-04-04)


### Bug Fixes

* move userId-based priority logic inside media type detection block ([6038d0a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6038d0ac543bfc15c13fb88ec894667c77deca20))

## [1.8.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.8.0...1.8.1) (2025-04-03)


### Bug Fixes

* correctly invoke update_document ([13fc8ce](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/13fc8ce23ab520c8df37f73bfadc2e560045f0dd))

# [1.8.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.5...1.8.0) (2025-03-26)


### Features

* set priority to the highest if user triggered action. ([1fb47f2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1fb47f2a8a17e1c214d4f4a2a528449dd7f31952))

## [1.7.5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.4...1.7.5) (2025-03-24)


### Bug Fixes

* staging should called CCE ai gateway ([2ef1324](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2ef13249cf85b81f62d0f00743bfe9f6f898a392))

## [1.7.4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.3...1.7.4) (2025-03-20)


### Bug Fixes

* use file path instead of file url on saving to milvus [#16608](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/16608) ([66c300e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/66c300e0b7df68d6e09044f20b7fcbb4ce8c1ce5))

## [1.7.3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.2...1.7.3) (2025-03-17)


### Bug Fixes

* 0000: Trigger pipeline ([52b1566](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/52b1566b4d581c7529946509d5bf1e440d91e944))
* increase default threshold to 0.5 ([fda6e3e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fda6e3e9ea6ebf09a50c896e42d9edaabb1c2e14))
* nude det video ([fb1bdbe](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fb1bdbe99ae794b9953fe1af499a070a74649e03))
* remove multiple defaults ([818d177](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/818d1774873fdde2c0dce60e772ef74128995178))

## [1.7.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.1...1.7.2) (2025-02-25)


### Bug Fixes

* timout increase ([4b84555](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4b8455508da62aa511bf9bfa917ecd72d74d0eb2))

## [1.7.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.7.0...1.7.1) (2025-02-14)


### Bug Fixes

* added timestamps when receing msg from msg bus ([dd16061](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/dd16061419929b90c5cffefa22710f07f01cb52c))
* bug fix in import ([1733a4a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1733a4afdca2904d83b578106a49c71e1bef3c0d))

# [1.7.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.6.2...1.7.0) (2025-02-06)


### Bug Fixes

* updated env variable ([438da6a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/438da6a6bea8bec0ddd63fdbf21bc6956d04a60f))


### Features

* added the recognition threshold as param in temporary analysis ([3424edf](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/3424edf2513906b8c8f85244e8389d199756293c))

## [1.6.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.6.1...1.6.2) (2025-01-17)


### Bug Fixes

* added support for reverse video search even in the case of redaction ([36fd4e2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/36fd4e243d529a4d409bc7e3193e6451a582ce1d))

## [1.6.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.6.0...1.6.1) (2025-01-10)


### Bug Fixes

* added ENV USE_LOCAL_ENV default set to false to call the internal ai-gateway ([6f069f8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6f069f8eb54ba22b36df488659b82c15ead7f84e))
* added logic to stop doing relationships for videos for redaction to save time ([20254a0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/20254a0668be3cdb33c97e1555a5c7f6a8231056))
* addeding a status when a message to store the item is received from message bus ([6fdcc19](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6fdcc19efbbc631c5446544ed3862430ac553383))

# [1.6.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.5.0...1.6.0) (2025-01-07)


### Features

* added move temp file to sas instead of backblaze ([e6a9049](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e6a9049dd656dc15844e5a9e840b1aa2a33cd747))

# [1.5.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.34...1.5.0) (2024-11-21)


### Bug Fixes

* added clear logs ([cbcca2e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cbcca2ead57bc52b4181bdf3b211bcecb3999e7f))
* call local ai-gateway to fetch results for NudeDetection and CustomObjectDetection ([53d2af2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/53d2af268fb4a1e73bd499ed34aa8fb1bdd1ab5f))


### Features

* [#14444](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/14444) change be to accomodate multi pax ([88b3978](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/88b397872ec3cee539f3a08f247b7c662053bdf2))

## [1.4.34](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.33...1.4.34) (2024-11-18)


### Bug Fixes

* dummy commit ([eb57039](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/eb570392392eec198e758a3b952fc1d13748e67c))

## [1.4.33](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.32...1.4.33) (2024-11-14)


### Bug Fixes

* added retry mechanism and increased timeout for updating thumbnails for videos ([a765d15](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a765d156c8c5db87bdd4965272b93658d023c821))
* allow redirects in update thumbnail ([43e398f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/43e398f4aa14dea53f11887f3ae1f4f2c2a79b45))
* minor bug fix ([6608c23](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6608c23c46324126f3ec85c46703b39d5eb7e50d))
* minor bug fix ([2b02561](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2b0256105f449d6e3a2f96a2a37a4a267bf60324))
* minor spelling mistake ([38cdeb2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/38cdeb27f879b735017ea80e56221f4d48ced775))

## [1.4.32](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.31...1.4.32) (2024-11-07)


### Bug Fixes

* fix the issue in processing profile images because, those images doesn't contain a fileId, and presigned url becomes too lengthy to store in vdb, now passing the original url through metadata ([d0b2e9f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d0b2e9f4eb3636f53e58a3c506c2d50eefaeba7d))

## [1.4.31](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.30...1.4.31) (2024-11-06)


### Bug Fixes

* bug fix in the ranking stats for FR ([c9cc3ed](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c9cc3edd4537474f4caff0836ececb337a28aecd))
* changed to requests library instead of httpx for google image search ([f7f7d5b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f7f7d5ba1187254781936309934d464b86985030))
* minor bug fix in proxies in httpx client for google image search ([7c7a230](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7c7a2300adab7f22c3887d2c8d38422380e4e665))
* minor bug fixes in the relationship creation ([d5da98a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d5da98a52f9049dd69798bc8c55eda94687df8fb))

## [1.4.30](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.29...1.4.30) (2024-11-05)


### Bug Fixes

* added Is_ON_PREM condition in making url public, added logger exceptions ([517b495](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/517b49508c70cd4e7e8bc8782d5f558af59547aa))
* azure get person name httpx stream not working ([0becc20](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0becc20de96eedf9676a3c8b87212c0a510a60ba))
* url_to_stream "stream" is not available in httpx ([05ebba0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/05ebba00febe848478c0ae52436b5c56a28c0d90))

## [1.4.29](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.28...1.4.29) (2024-11-05)


### Bug Fixes

* added logs ([ec2e030](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ec2e03041100a85ef505cd49c1d8cf92c04198dd))
* minor bug fix ([9ee50de](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9ee50de7b1577404da73abb064dead0e42fa0401))
* minor bug fixes ([499a508](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/499a5081265632cdc07e1c01149b019496f3253f))

## [1.4.28](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.27...1.4.28) (2024-11-05)


### Bug Fixes

* added increased TIMEOUTS ([a2ed91f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a2ed91fe41fd9e39747e6afa7f906f221c673359))
* converted a lot of functions to async and changed requests library to httpx ([91abbe9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/91abbe93cc66821820db69c0e0cd8efa846ef8ab))
* minor bug fix ([5ca81b2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5ca81b297e4664ff527d30c4b3866e9045201f4d))
* minor bug fix ([a39c068](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a39c0683abde10aa3430dd4790e9f2a9c265a9d6))
* minor bug fix ([41102dc](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/41102dc34d240199bcb364f6f27506cc079dd9d0))
* minor bug fix in listener ([81d1c08](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/81d1c0863d7ee682893aeccc799b87d48177c48f))
* minor bug fix in listener ([f04ad9a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f04ad9a88d2b00da068ec7ffb6e1be8a84992bf8))
* moved the localhost endpoint calling to the function calling ([9a91345](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9a913453616595f681c396e510e22ceb32c50962))
* replaced requests library with httpx ([065185a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/065185ad9ef4f3c3839bed6ee8ce5af5ea54bd72))

## [1.4.27](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.26...1.4.27) (2024-11-04)


### Bug Fixes

* added env variables to improve azure serive bus ([9d9913f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9d9913f7e97e21d7e39256795a4e090a25d9f3af))
* added logs ([b35f671](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b35f6710beed45025ea7f7b21191aa8f7be7c5e4))
* added more logs ([cd23775](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cd23775aa9c81f6c0c0a9a532ed69021833ba83f))
* donot complete the msg if it is not from this env ([8adc865](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8adc865b0e890d845413c5c48669eaaa0b2c1933))
* minor bug fix in DEBUG variable ([93bfc1c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/93bfc1c21ddcf7225dbaf617b84cd4cf78321514))
* minor bug fix in ENV variables ([e4abd79](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e4abd791f3a7cde390bc1d0db78caab92c994150))
* minor bug fixes ([351f4f7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/351f4f7685c4f12494bbfbc18d635cee695335c8))

## [1.4.26](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.25...1.4.26) (2024-10-29)


### Bug Fixes

* added auto_move_file_to_backblaze to all the missing places ([92b62cb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/92b62cbd2c8349b9a9b9370a5359aab7f55d4d43))
* minor bug in the gpu setting ([cdd1b9c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cdd1b9c5b99802c9646b62e7f751d1596a559005))

## [1.4.25](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.24...1.4.25) (2024-10-29)


### Bug Fixes

* added --preload to the gunicorn ([df5f0e5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/df5f0e5b718095bafc80731d5caa215ce8d386cc))
* dummy commit ([2181118](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2181118807096f8e8bffb574426207d2b5038fb7))
* moved the model download part to the init container ([74b7898](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/74b7898d247085aa5e1c9e2bc719db23584e304b))
* moved the model downloading part to the container instead of pvc to support replicas ([dfdab19](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/dfdab19173d40de22de7a6b294501d4e857fa169))

## [1.4.24](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.23...1.4.24) (2024-10-29)


### Bug Fixes

* added gunicorn to Docker file ([95f256d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/95f256d986ac102d0a9d5a52af26afe48eec69a2))
* changed the name init to start.sh ([7d05060](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7d050609f5544a5a0457a0feb9ffb200ebf2fc7c))
* just added logs ([f3eb026](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f3eb02652909057550a35b9bc4e03febce4db6d9))
* minor bug fix ([d37c27d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d37c27dab0ff9941d61530e2b12b09205c2ae385))
* minor bug fixes ([aefe23d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/aefe23d49866b130f3d42085642746f0e9b672a5))
* removed the code to not save the face_landmarks becuase we found a solution, initially if the field is set as Dense vector and later float, causing mapper exception, just manually or automatically, set the field to float, it will start to work ([9e60af2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9e60af2facbff2198f020698443ab36e359556f1))

## [1.4.23](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.22...1.4.23) (2024-10-29)


### Bug Fixes

* added guvicorn to the server ([6f78a10](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6f78a10116c9650d6d58a7288a068adec8fc1ad1))

## [1.4.22](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.21...1.4.22) (2024-10-28)


### Bug Fixes

* added NUM_RETRIES and WAIT_BETWEEN_RETRIES as env variables in the retry mechanism ([4bb7a52](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4bb7a52ce6c16750d3a13c2ab8592362135f0980))

## [1.4.21](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.20...1.4.21) (2024-10-28)


### Bug Fixes

* change the key name persons.face_landmarks to persons._face_landmarks ([5b15737](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5b15737eadad1389d6bd8fd2387fd94dbb85ed4a))

## [1.4.20](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.19...1.4.20) (2024-10-28)


### Bug Fixes

* added retry mechanism to the auto_move_file_to_backblaze ([8240f8e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8240f8e4098dc0a2b8d051819b15e452b37e5146))

## [1.4.19](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.18...1.4.19) (2024-10-22)


### Bug Fixes

* added nude detection flag after processing evven if there are no detections ([e368400](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e36840073ecac9e62e6ff1ad7134ad82053386e7))
* added Nude detection for images ([d73bc18](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d73bc18fd95d1b666db9a1fc8a9543f24d7c3c6a))
* added the code in temporary analysis to support more than 1 people in image search ([a1f8b93](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a1f8b93a3773c6bbbc7cfadd0310878e4bceff10))
* bug fix in nude detection receiving ([ff8663d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ff8663d80c48239943204a25a481d8145bd841d0))
* minor bug fix in nude detection processing ([99f2d80](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/99f2d80f7faa2c8111480db8d6afce794b83c7e9))
* minor bug fix in the nude detection flag ([7a76b98](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7a76b9840cbde27588946a31a99fb6e667b0c446))

## [1.4.18](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.17...1.4.18) (2024-10-15)


### Bug Fixes

* added pagination and case_id to the image search ([92eb16b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/92eb16bda30888b912d20643234814ca6917de33))
* update the img_url to the original one ([07fcf8f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/07fcf8faf8dc4412f2d5550e0f9db5e3b7944fb6))

## [1.4.17](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.16...1.4.17) (2024-10-10)


### Bug Fixes

* added ephermeral_analysis and saving image embeddings to vector dabase even for redaction ([4450ff8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4450ff89a9cd8b34b51f6646233a6ef485d3a5a0))
* added more debugging in the create_relationships ([9393da6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9393da61b776da71922b03495bfb666dd225a901))
* fixed the port number ([e6b3bc9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e6b3bc973345d2168d13698cbe5a17dffdedce03))
* img_url updating with file img_url in milvus insertion ([3a0f50e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/3a0f50e2313b6f8445a58d0b210bb99c175a8a97))
* missing dependency ([1b7e233](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1b7e233052fed1a7373273eb612572e97dc11316))

## [1.4.16](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.15...1.4.16) (2024-10-01)


### Bug Fixes

* added logs ([636182e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/636182ee6499198d68c4a79bf2cd7212695688b4))
* fixed the bug for mediaType in submit ([f145e58](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f145e586b8a80662a54078a7438c9efe298b324e))
* modified make_url_public added support for urls starting with docserver2 with requestfile endpoint ([40abcb9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/40abcb96e9a856bad220b01905582f755902a5e2))

## [1.4.15](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.14...1.4.15) (2024-09-27)


### Bug Fixes

* audio transcription results store fixed ([4f5dace](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4f5daceb6148ea5d8386e47cb3ab3e881b6b44dd))
* returning Unknown if name cannot be detected ([663ef53](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/663ef53e3b5bc0a3aefeb6f3c48897de3bb89ba9))

## [1.4.14](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.13...1.4.14) (2024-09-23)


### Bug Fixes

* dummy commit ([e7c6c12](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e7c6c1225eae7e50a17e7f646ca98cd41551c238))

## [1.4.13](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.12...1.4.13) (2024-09-19)


### Bug Fixes

* bug fix for keyframes in the listener ([99e334e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/99e334e0e5370ef51f377b2e165850edfc167f96))

## [1.4.12](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.11...1.4.12) (2024-09-19)


### Bug Fixes

* added code to call the same env ai-gateway to process services that dont need cce ([2668a61](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2668a6161e253f1940cc86dcb9cceb6b3aea98c1))

## [1.4.11](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.10...1.4.11) (2024-09-19)


### Bug Fixes

* error handling ([d9e4905](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d9e4905abdbc25c3fc79ef38a65c3551b4404a84))
* minor bug ([153467d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/153467d13c22f3849b04053bc0a1083570d01e12))

## [1.4.10](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.9...1.4.10) (2024-09-18)


### Bug Fixes

* added code to update thumbnail for videos ([aa87d63](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/aa87d63f60009e5ff8a9126ee31e89221120cd4f))
* dummy commit ([0aa308d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0aa308d26c9e52f321198da8e53fb75fd46d4b6e))
* fixed the awaiting issue for thumbnails ([8d6b857](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8d6b8575cfa9410dc2241d0f73b3cb505dd5018a))
* fixed the code with thumbnail update ([b2a09c0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b2a09c060c73de555b5f43f7006a3f03de0e515a))

## [1.4.9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.8...1.4.9) (2024-09-17)


### Bug Fixes

* fixed the issue in get_person_name ([c9fc432](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c9fc4328de9ebcba3d005ce229a2b5269462c6b8))

## [1.4.8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.7...1.4.8) (2024-09-16)


### Bug Fixes

* added code to create relationships for videos ([ea71068](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ea710686eb9278ce3cab2cdf7ffd6d6cf2406bf6))
* bug fix in the name retrieval fro azure in relationship creation ([007e41b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/007e41b34c6e79f42ae780419af1c62e95e3cdfc))

## [1.4.7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.6...1.4.7) (2024-09-11)


### Bug Fixes

* dummy commit ([2af99b7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2af99b7878ece650f2e8a184fc1ae0ffc8d33bc8))

## [1.4.6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.5...1.4.6) (2024-09-10)


### Bug Fixes

* added range filter to 1 for profile image matching ([272d690](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/272d6900f93a697aecd3539f963c8b922e8ea727))

## [1.4.5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.4...1.4.5) (2024-09-09)


### Bug Fixes

* added code to update the gdino flag ([8110d98](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8110d9820676e387b322b934faffff30b672f360))
* added logs ([5d95906](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5d9590643c6b487d6dbfd2b324cbae4fdeb2c854))
* added the endpoint to get the name from the azure and the glens ([2b86a45](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2b86a454b5cb7a3248d108d40b5ac9d2931c21ba))
* added the ranking endpoint for logo detection ([fd93656](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fd936565a2fe98598440f2fd88eddfb5bd4f41e5))
* minor bug fix in the update function of the Face_Entity_Mapping ([162eec9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/162eec9b7279ee988e108cae412e9affcf94a343))
* update gdino object detection flag ([3b8d316](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/3b8d3164e89efb6dec911d12b3cacf9cf65abad1))

## [1.4.4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.3...1.4.4) (2024-08-28)


### Bug Fixes

* bug fixes in the person name update ([184e6c4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/184e6c491140998e5dce0210485a67f5ea3e1ad1))
* small bug fix ([5927df5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5927df53c84aeb483382d7a2e847dc630c60b649))
* updated update_Face_Entity_Mapping from old format to new fomat to support elastic field creation ([578f9e1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/578f9e1a8e4cdb64dc162b63a3d5f69cd851b991))

## [1.4.3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.2...1.4.3) (2024-08-22)


### Bug Fixes

* added flag to update file when it is sent to analysis to ai-gateway ([0d270a6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0d270a6c664061d8740207e5d2cc9fc7182c4904))
* added missing dependencies ([4570fe0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4570fe0bfaabc37f3995e89d7b19d76acb048989))
* added ranking statistics routers ([84a2066](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/84a2066753956344135240b3d714d9791ae6efca))

## [1.4.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.1...1.4.2) (2024-07-09)


### Bug Fixes

* not doing vectordb operations if the redaction is true ([5fb313d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5fb313d6b2b3144e76fcbe99f305f70ef201716a))

## [1.4.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.4.0...1.4.1) (2024-07-09)


### Bug Fixes

* updated the docserver v2 locate endpoint ([5f7703f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5f7703f652df2f455a0d3836ee2004fb9d0fd5de))

# [1.4.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.3.1...1.4.0) (2024-07-09)


### Bug Fixes

* dummy commit ([66d5fdd](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/66d5fddc8ac953265624261075065d6cebde6df6))
* fix bug and update condition for onprem ([ca4b596](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ca4b596868adc158825f249e302fe85a6cfbf0d7))
* pass the file url and not file itself ([6ffee90](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6ffee90e9c4e003f0fc532b8821ebb1a98f8be85))
* use file_server_download url if the url starts with / ([f66802a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f66802a7707a0ca620a69088b3b8c8b4264e1087))


### Features

* **#9711:** added force_move to backblaze ([4c3d1a7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4c3d1a7861894416fd501ff317c446f9eb94c2c6)), closes [#9711](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9711)
* **#9711:** added None checking ([09995ac](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/09995ac7707c731f97b73402f203957e28eb4da8)), closes [#9711](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9711)
* **#9711:** added upload to backblaze before send to azure analyze & google lens ([eb11732](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/eb117327ed05dfa409c5dd9a7c4b74d8141e52bb)), closes [#9711](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9711)
* **#9711:** added upload to backblaze before send to google lens ([c4c22d5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c4c22d5d36f5e90c0bf8e5638ab2b6181eb348c0)), closes [#9711](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9711)
* **#9711:** public_url recalculated twice ([b6307b3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b6307b3097d2a0576e1d58178eff56d111f9af20)), closes [#9711](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9711)

## [1.3.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.3.0...1.3.1) (2024-07-01)


### Bug Fixes

* updated hardcoded docserver url ([c7d4f36](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c7d4f36698b0e4522e1c4e5b9ffd22f017cd530c))

# [1.3.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.2.0...1.3.0) (2024-07-01)


### Features

* **#9622:** fix Optional typing ([3dd1083](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/3dd1083e36fd3bbf10009c9f9d75d0f2046aed0c))

# [1.2.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.1.1...1.2.0) (2024-07-01)


### Features

* **#9622:** added ON_PREM option & auto move to backblaze ([ce7e30e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ce7e30e47aa10025d39fa258cfd2e89d4b496a8c)), closes [#9622](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/issues/9622)

## [1.1.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.1.0...1.1.1) (2024-06-27)


### Bug Fixes

* fstrings for docserver_base_url ([5fc2015](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5fc2015a0536daf9d92d4518fde02b6a33644d9d))
* Updated vector_search_router.py ([b21add5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b21add50096efa8cf9288d0996025206a6ef2483))

# [1.1.0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.48...1.1.0) (2024-06-26)


### Bug Fixes

* added logic not to get names, save vdb record if the redaction is true to save time ([0646beb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0646bebada52e5a801b91688554f2ba6121d9a02))
* replaced hardcoded values to take from env variable ([90f105d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/90f105d2c3f9db008c8dad5f1240217d4fe52106))
* skipping clip embeddings if redaction is true ([b331b10](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b331b10b2e7cfbc80804bbfbc84a4f69d5eed1de))


### Features

* Add OCR results to main artifact if file belongs to an artifact, also added fileprossor status, back to artifact docprocessor status of the artifact ([77c7df4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/77c7df4a44bcbc96284521c8c704141052d95e15))

## [1.0.48](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.47...1.0.48) (2024-06-03)


### Bug Fixes

* add Text from OCR to Artifact ([a9cbe0d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a9cbe0dc40528cdb7bd8cd7ac9770012bea903b8))
* bug fix Updated submit_router.py ([9fb4050](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9fb4050934c8812bdcdec0fcf4d91aa1bd59982d))
* converting to public url Updated submit_router.py ([d45b998](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d45b99886c1ff6dac27872c1ba1cf19a531a4222))
* dummy commit ([05031c2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/05031c293445266f2a25951668e4eefbd38d6da7))
* Save OCR results to main artifact if file belongs to an artifact ([4360b93](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4360b9308687d19b9f47c84d2487186484f42d72))
* Updated submit_router.py ([b7c2471](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b7c2471d1bbc2345c557920205755571ce454fe6))
* Updated utils.py ([757c610](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/757c61019835855280cd1b5a2fe60b9498f81144))

## [1.0.47](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.46...1.0.47) (2024-04-24)


### Bug Fixes

* Refactor get_person_name function to handle face_info and face_entity_mapping in media_property_retrieval.py ([9a8bb33](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9a8bb331ec2d58528e1acc035485267e5a61bce4))

## [1.0.46](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.45...1.0.46) (2024-04-24)


### Bug Fixes

* added logs for google time taken ([b4ebc0b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b4ebc0b63c566fc4ffaee4301aa63412019bd870))
* logging time taken for all people in the image to process relationships ([87fd3e5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/87fd3e5249475652866234d920185bf178e64cb7))
* read img_info for every persons, to take the updated values from azure_analytics ([f5ed416](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f5ed4168ce9e7fc9113c3ed997cc094238fbc58e))
* Refactor code to measure and log the time taken for processing each person's face in create_persons_relationships function ([ace0a3e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ace0a3eb0210094334169f9f0c9b4785af5a3fcb))

## [1.0.45](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.44...1.0.45) (2024-04-24)


### Bug Fixes

* modified get_person_name logic, removed the condition to check teh comparison between facecrops and google names detected. this is very inaccurate and slow ([0ddc875](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0ddc8752c215191177a0f71e895487fafe1673c4))

## [1.0.44](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.43...1.0.44) (2024-04-22)


### Bug Fixes

* Fix exception handling in submit_router.py ([5bcf68a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5bcf68a189bfd4e3ab0557ae1b03266c6526600e))

## [1.0.43](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.42...1.0.43) (2024-04-19)


### Bug Fixes

* added create_image_entitycard_if_doesnt_exist function ([694a1eb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/694a1ebb57cdeca4bab80c91481e5072cdfad253))

## [1.0.42](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.41...1.0.42) (2024-04-18)


### Bug Fixes

* Add support for PROVIDENCE_PUBLIC_HEADER in make_url_public function ([aed4318](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/aed4318a60ac5878881ddb914ff23c06e2e135dc))

## [1.0.41](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.40...1.0.41) (2024-04-17)


### Bug Fixes

* added feature to update the FAce_entity_mapping ([7d2db27](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7d2db279168fb9479d578ba299c652620e4f1332))
* added verioning to albulentations and pydantic ([fb74895](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fb74895d526544c6ae76eaf59f1f817b0c90dfed))
* added, PV public header, added feature to update the Face_entity_mapping ([780486f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/780486f64352c98620900f1db933d421b7216730))
* added, PV public header, Update Dockerfile and init.sh to remove installation of insightface package ([2d811fa](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2d811fab49e3277f9360e8b5611d82aa1e730001))
* bug fix ([f9d3621](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f9d36211e89d0d530fc48d442442816b308c7a05))
* modified pydantic version ([9437b73](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9437b73b15a2b614d7963cd471a49c853ce054cf))
* Refactor get_person_name function to update Face_Entity_Mapping and entity card person name ([be5997b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/be5997b49f01a0b0baf7f134151ee562303d1939))
* Refactor get_person_name function to update Face_Entity_Mapping and entity card person name ([a39ad28](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a39ad28c77067e5fd3825fc3ea81e450208956b1))
* Update Dockerfile and init.sh to remove installation of insightface package ([c9e10d0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c9e10d053e72f154a73745fe4448567c1bd988c7))
* Update pydantic version in Dockerfile ([ae17fe1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ae17fe1cb82d566dcb47ede932f72eba57676105))
* Update pydantic version in init.sh ([76a3bd5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/76a3bd56b42fa423820209d63712d0d4c2d36e83))

## [1.0.39](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.38...1.0.39) (2024-04-16)


### Bug Fixes

* ImageSearchFromImageFileId, Modify the Similar Images Logic such that if there are people, use different logic than when people are not present in the image ([e2843e3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e2843e384bcc7095642d1f85f16f1492e99dd7d4))

## [1.0.38](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.37...1.0.38) (2024-04-15)


### Bug Fixes

* Refactor create_persons_relationships function to update person name using Google Lens in relationships.py ([79c1e07](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/79c1e078bc4a7daaa6dfc20a57ea8edcba482cc0))

## [1.0.37](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.36...1.0.37) (2024-04-13)


### Bug Fixes

* Update face_url and face_id in vdb_payload when reuploading file in receive_router.py ([166bfeb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/166bfeb3d1f039329e597df4672cd2fbd2bad93f))

## [1.0.36](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.35...1.0.36) (2024-04-12)


### Bug Fixes

* Update face_url in vdb_payload when reuploading file in receive_router.py ([011f442](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/011f4420b2342ba23803b98f82d9217ebeaeab0d))

## [1.0.35](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.34...1.0.35) (2024-04-12)


### Bug Fixes

* Add range_filter parameter to get_similar_images_by_face function in vector_search_router.py ([a688d11](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a688d119d55b403c8a8f098e689c0ca3fc93df7a))
* added logs ([fa29900](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fa2990043af6c829058d6cb19db5807106114ce7))
* added logs ([5207f4d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5207f4d34c0d32d107a148e486cd5a17046e575b))
* bug fix ([f38415d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f38415de91185629b21c6e048d27599f0e3f60db))
* bug fix in get_person_name function to handle unknown person names in media_property_retrieval.py ([117eaa8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/117eaa8db041418c8f141c5d80bfbb5d9a1d46e0))
* bug fix in person name retrieval from similar images ([5da2e35](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5da2e354549483328fe118d730e2a10811605ba7))
* reduced redundant logs ([790f190](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/790f19051a9752e3aa091b9c8f6c27f697e24d6e))
* Refactor code to improve performance and readability ([aa9c080](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/aa9c080a10a8129b10eccc1ef62e5fe305e4e662))
* Refactor create_persons_relationships function to update person name using Google Lens in relationships.py ([1e0fd35](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1e0fd358101210d67f87888227ed0061f2bdf033))
* Refactor create_persons_relationships function to update person name using Google Lens in relationships.py ([5f81c18](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5f81c18b290335ca473a14aca2e2c7c522fb3efc))
* Refactor get_person_name function to update person name using Face_Entity_Mapping ([fdb01c4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fdb01c428264afdadc652201337c7b08cce07ae2))
* Refactor update_entity_card_person_name function to fix parameter order in utils.py ([b2898eb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b2898ebe944b8b238afde2caf3adb35b8a51b2ca))
* Update face_id in vdb_payload when reuploading file ([8e18193](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8e1819342de78b9ad3f672fc57078e09d5dd9f13))

## [1.0.34](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.33...1.0.34) (2024-04-05)


### Bug Fixes

* Add logging statement to store_data function ([e73e7f6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e73e7f64b258cacb031881ec085085a596cbc111))
* Add timeout parameter to client.post requests ([a33fa0c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a33fa0c19475476138017677b83cbfdfa7db81f5))
* Add timeout to HTTP requests ([86ada92](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/86ada923b612afd9c2fa46dd6d183717680f6451))
* bug fix in URL in get_case_document function ([bc9388e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/bc9388e7a6e7a6ac5360bea4d59568974d339ede))
* Refactor person name check in store_data function ([f13cc94](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f13cc94e46856826acdf7196e136f57bea4f3008))

## [1.0.33](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.32...1.0.33) (2024-04-04)


### Bug Fixes

* Add CLIP embedding functionality and file processing improvements ([ac2e2b7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ac2e2b710f71e27109155579e0179750afc3b053))
* Fix file_info retrieval in submit_router.py ([fe15d6d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fe15d6d3c246352a4896b1809e5712a17fd3dc5f))
* Update Dockerfile and init.sh to install Python dependencies ([7f0f23f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7f0f23f0d173c38b9a117cff0dd93f03e14ef18a))
* Update person name threshold and add Azure analytics for similar images ([1ca8393](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1ca8393aec578fbfef6efe0b7b01b04b23568f36))

## [1.0.32](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.31...1.0.32) (2024-03-28)


### Bug Fixes

* bug fix in reuplaoding face crops in the video processing ([bca3da5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/bca3da5120b6042f1f0cc73a3ab60d897de6524c))

## [1.0.31](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.30...1.0.31) (2024-03-28)


### Bug Fixes

* Refactor fileUrl in video processing for FR ([2089f48](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2089f48d81d0be8681e2998e3c3a9a43a6ecaa43))

## [1.0.30](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.29...1.0.30) (2024-03-26)


### Bug Fixes

* Add person_data field to InputRequestPayload and get_person_name_vdb function ([46a40c7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/46a40c709d71125bf674286019635296a4bdce7d))
* Add support for Face_Entity_Mapping in get_person_name functions ([550cd7c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/550cd7cfaee48e0c2393f5ad2d2803b12dae5ac2))
* added code to create relationships for all the images that are processed ([32678cd](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/32678cd8b186b544b7bc9bfe92643a4e2d60641c))
* added dev container ([92251d8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/92251d82ceb6d533ca9919e2383ed9b708ed5ebb))
* added logs ([e4fb9de](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e4fb9de01baea61106787e580867ede68317b716))
* added vdb upsert endpoint in config ([e9af2fb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e9af2fb8d59528959972e74624e650f9aa66b4f1))
* automatic azure name search for images in the receive endpoint ([7627bf5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7627bf5336da09c345f78ef403f457cca7d6d643))
* automatic name detection using azure if the name is not detected for face ([cf700af](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cf700af206075f2480cf6e5ec54b7adb04dfbf3e))
* bug fix ([a01f738](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a01f738a7832076ecd997353856522477888aadb))
* bug fix in face crop re-upload for the profile images ([a704c8d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a704c8d7a240a37da27e2944ab1670f0b623ed58))
* bug fix in face_mapping, changed the structure and name of the field to Face_Entity_Mapping ([4055181](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4055181dc26cf5b2cdd4e2f1b5f757e41537ac61))
* just testing ([9faecf0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9faecf003085a1acd23ff49b33c05629155bf134))
* Refactor code to handle person data in get_similar_images_by_face function ([1a33944](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1a339440d8d3b84a95804eaafbc29c7253aa1f08))
* Refactor person name retrieval logic in media_property_retrieval.py ([ebbb267](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ebbb2671f9c7b2862c857dc86543280a7466f2dc))
* Remove duplicates and improved inconsistencies from celebs_detected and update logging messages ([4744cdf](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4744cdf8c8693978ec10a6bb0a42d3d964ba9925))
* small bug fix in the person name retrieval ([f4ae280](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f4ae280a43ed6a971d01e15122eb32bdf8e20874))
* Update distance value in Face_Entity_Mapping ([b6ccc41](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b6ccc41d68e6a9928133bf3a96b2cb21b23fda71))
* Update logging messages in receive_router.py ([9cffd39](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9cffd396fb419a282c0a390e6c5eb5c849815470))

## [1.0.29](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.28...1.0.29) (2024-03-12)


### Bug Fixes

* Add entity card update for facial recognition results ([c05d1e8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c05d1e84db26545d45980a78b38bf61b14a5fc79))
* Add facial recognition processing for profile images and non-profile images ([5381704](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/53817040eef7e45bbc929e7140bfccc4d4f51319))
* Add metadata field to InputRequestPayload class and update submit_request and submit_zip functions ([b49d11a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b49d11ab1f8c5882a2b177c84bb06bafe1d0e50b))
* bug fix ([1e4bc46](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1e4bc465e09b3da5306d9b5fe314a656db458e1e))
* Fix data assignment in store_data function ([a6e0c9e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a6e0c9e3aa1c74cc8f90fe31d9cf7b6ba165533b))
* Fix social network source and entity ID extraction ([709f58e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/709f58eba94c206176a741853c0b446bd64fb670))
* re-activated the relationship endpoints ([2c634ca](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/2c634ca66834445361fdf946afd8be9e4ab8e368))
* Refactor facial recognition update logic in store_data function ([fb60e27](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fb60e27d803b3cafc39292514987c006acea90f4))
* send taskId to get the data from the ai-manager ([7fa0eeb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7fa0eebdf22cc03034e033c403e8a4d47e63bc40))
* Update facial recognition results in store_data function ([918c6cb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/918c6cb0fec8ab5998f283ed6ed466aff98afc77))
* Update metadata to metaData in submit_router.py ([56da5d8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/56da5d820f08e40fdafce633dd9da023b09e8fbc))

## [1.0.28](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.27...1.0.28) (2024-03-08)


### Bug Fixes

* Add dependencies and functions for image analysis using Azure Cognitive Services ([0e95362](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0e95362e12eb1851c3d30a05015c3f89e44fb3e5))
* exception handling in azure analytics ([31811f0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/31811f07c704a4c9295942af99ff0f3ca4a82e30))

## [1.0.27](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.26...1.0.27) (2024-03-08)


### Bug Fixes

* Add common image relationship endpoint ([1017b4c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1017b4c121d36d5c6184f777f75f868d605a5139))
* Add optional expression parameter to similarity search payloads ([e6dd5d8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e6dd5d8f903a2e50c4f4d5664faadf832c87dab8))
* Update image ID in convert_similar_images_by_face_to_rels function ([f586710](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f5867108b72f87c357b016a89d2ed842fe895ea7))

## [1.0.26](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.25...1.0.26) (2024-03-06)


### Bug Fixes

* bug fix in get-people-relationships ([0086b82](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0086b82fafd44b2c59a204bf95a7876a7c8ce32b))

## [1.0.25](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.24...1.0.25) (2024-03-04)


### Bug Fixes

* Add handling for no data from Google Lens ([011503e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/011503ec3b9831e40e7d5db54639c2f0874fd199))
* Refactor logging statements in media_property_retrieval.py ([074e4b5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/074e4b546b05c8c2b01d48d6e764cf05c88dde4e))

## [1.0.24](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.23...1.0.24) (2024-03-04)


### Bug Fixes

* Add endpoint to get entity images ([25963c3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/25963c353a1213abcb99c1d7fe0418122ce8d781))
* Add logging statements to retrieve person names ([4c6dd9a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4c6dd9ac630fa3cf37fc0ffebc7a26801465d594))
* Refactor media_property_retrieval.py and utils.py ([e4dfda7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/e4dfda7b9936bd6dee77f8269686a39fd2478a04))
* Refactor media_property_retrieval.py to improve code readability and handle multiple faces ([33b3d54](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/33b3d54a7574461060e38ce938030cbfb1242e77))

## [1.0.23](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.22...1.0.23) (2024-03-01)


### Bug Fixes

* Add logging for the number of items in the receive payload ([91cbbd8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/91cbbd8d14e65da8004debff7f532314e5359006))
* Add logging for unique items in receive payload ([d08c491](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d08c491750c18283d1459abc2bcd25ef5ddb50b8))
* bug fix ([7ab7931](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7ab79314c863b8deda1a9df76416d948ea61990e))
* Fix file URL retrieval in media_property_retrieval.py ([f6d6642](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f6d6642518b4aafc8899a2b7efec7811c65a003d))
* refactor to not to call google api if the num_faces==1 ([f2642c6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f2642c60e27e0f5bf00d3a730440c5fa03dc6fe7))
* stopped creating relationships in the receive endpoint ([20daf1a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/20daf1ae2cad913e24de864f408620d3bc5fdf40))

## [1.0.22](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.21...1.0.22) (2024-03-01)


### Bug Fixes

* Add endpoint to retrieve faces of images in relationship format ([80d4d98](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/80d4d98ca55d973595351959ad1613141e7837f9))
* Add fallback return statement for person name not found ([6e09277](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6e0927785f15ef9fafe43269af4d197b4ebdcaec))

## [1.0.21](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.20...1.0.21) (2024-02-29)


### Bug Fixes

* Fix issue with celebrity list retrieval in media_property_retrieval.py and add file_type field in FaceDataPayload class in vector_search_router.py ([99f34d7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/99f34d71d07ecad2f1086f40fc89e495a097247f))

## [1.0.20](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.19...1.0.20) (2024-02-29)


### Bug Fixes

* Add deepface library for face recognition ([9bfde5f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/9bfde5f27894b6a9e6c97188084f48734897b1fd))
* Add default values for e_id and entity_or_case variables ([340e846](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/340e84660cddd84e55c064d8ef2285a6f39d71b0))
* Add face recognition functionality using insightface library ([48fedd9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/48fedd9277b29b2b315a878b8520b4a3fd16206e))
* Add git and g++ installation to Dockerfile ([f7a8d51](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f7a8d51b60f906a07f504107388f2b0655e60359))
* Add logging statements for case card creation ([f25ecdf](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f25ecdf6f1026a0ea3ace41aef1e2b6c9f47b41f))
* Add logging statements for creating relationships between entities and images ([1b1037f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1b1037ff7a9e8b8b49410bc77e87d3ac34cce3cf))
* Add noninteractive DEBIAN_FRONTEND and install libgl1 and libglib2.0-0 ([4ce49bc](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4ce49bcd22c1f3023671f92fbf7364500e8e825e))
* Add utility functions for creating entity cards, retrieving entity cards, creating relationships, and retrieving case documents ([29bb72e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/29bb72e383bb6b63a163e7f42f10bde40f6526cb))
* Added 2 features to get the similar documents from the google lens for face crop and main image ([64b1ea4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/64b1ea4a0297ba81f939fd28f9850129c5cfdec3))
* bug fix for similar images by face for videos ([c2be98b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c2be98b97867a8394ad897f7df67f47b12622c07))
* bug fix in getting entity or case ([6c7ba7a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/6c7ba7ac9a531d6a5177bcdd7f8b44e09ab95b24))
* Fix person name retrieval from celebrity list ([f10f853](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f10f85399827e34c339a52d16514cbbef2e1489d))
* Fix typo in variable name for images_main_image ([a53a7b2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a53a7b2f8c3f8a7b75e64ecb4af152cc8ff24f77))
* install insightface ([59694cd](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/59694cdaea9e2a4e377c728ffba0fede0bd8b4b8))
* Refactor entity card creation in receive_router.py ([1b1d24f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1b1d24fdb1e4549f50aac6cfe314eb3e69c0120c))
* Refactor media_property_retrieval.py to handle cases where person name is not found in similar images and google lens ([72c4b16](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/72c4b16becbe2cc22644bcbea7da665fcccdc7d5))
* Remove deepface from requirements.txt ([edf13c5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/edf13c5cd7a32f44ad83d4f29443158323bf2ed3))
* Update distance threshold and refactor person data structure ([d8d6244](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d8d62445ba7438dbce31d4264b382e211290176f))

## [1.0.19](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.18...1.0.19) (2024-02-27)


### Bug Fixes

* Add reupload success log and save payload to elastic search ([b8b1eed](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b8b1eed130d1fd9f195046535780824df53fe452))
* added code to to use CCE for DEV ([44aa21e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/44aa21eaf58f77912a5a549cac9e590dace5b1cf))
* bug fix in person name finder ([b61845d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b61845d68537845337d05fb8c01afb7bf894264b))
* Fix condition to check if face_info exists before checking for "personName" ([517370e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/517370e850fbe9ccdceb921ea47f838b82cc9580))
* Refactor code to remove unnecessary lines and fix indentation ([0c8b54c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0c8b54c7df6a0e6d9553ac867fcc9dc33e213d8d))
* Set STG to use CCE and DEV to use local ([fb2ce07](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fb2ce072e8113b288eae0b35115647fe5d28930d))
* Testing reupload in DEV ([74d29c0](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/74d29c0178da1243910b0a99509cb1aa8e3d4b78))

## [1.0.18](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.17...1.0.18) (2024-02-27)


### Bug Fixes

* Update OCP_APIM_SUBSCRIPTION_KEY environment variable ([76c7ca9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/76c7ca90a553a4caee14377a50a46b932cac63f3))

## [1.0.17](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.16...1.0.17) (2024-02-27)


### Bug Fixes

* configured STG to use CCE resources ([418261f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/418261f8f1d3b35a8f9181a07844bd4e22a88b20))

## [1.0.16](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.15...1.0.16) (2024-02-27)


### Bug Fixes

* Fix file source URL handling in upload_file_using_url function ([a5ebdf9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a5ebdf93b5b2fa4cc47bb19a67ede24d59fe3507))

## [1.0.15](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.14...1.0.15) (2024-02-27)


### Bug Fixes

* Fix error message in add_task_to_ai_gateway_queue function, listener.py function ([4d0e815](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4d0e8158db32e57ee6b1b127f462493219a1a575))
* Refactor error handling in listen_to_service_bus function ([24ce6e4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/24ce6e4ada77699a0fa4e1a3551f580f6c5cfc78))

## [1.0.14](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.13...1.0.14) (2024-02-26)


### Bug Fixes

* Fix image URL handling in vector_search_router.py ([5758e49](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5758e493d5bf0334c48f5077f0d32409ed26817e))

## [1.0.13](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.12...1.0.13) (2024-02-26)


### Bug Fixes

* Add FILE_SERVER_BASE_URL to face_url ([33fcb23](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/33fcb23ba4e9a3aea15366e22360e14d14c94a4b))
* Refactor face_url assignment in fr_reinsert_vdb function ([57c8607](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/57c86070ba6a460846d1e92967b77af5a61cf57b))

## [1.0.12](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.11...1.0.12) (2024-02-26)


### Bug Fixes

* Add fetch_unbackuped_files function and fr_reinsert_vdb endpoint ([d0de50f](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d0de50fbb634368b860c87bbb183db55a722f460))
* Fix handling of missing blur_score and alignment_score in fr_reinsert_vdb function ([b6547f8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b6547f88c234bde4537b4036ecca83afb7298c90))

## [1.0.11](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.10...1.0.11) (2024-02-26)


### Bug Fixes

* Remove unnecessary dependencies and files ([7673dca](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7673dca3c7f1828dece36f9ce142953a05a7f76a))

## [1.0.10](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.9...1.0.10) (2024-02-26)


### Bug Fixes

* Add environment variables and install GStreamer dependencies in init.sh ([7c558cc](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/7c558cca3f4aa9978f5ba4ed94bb4140b51c6300))
* Add proxy URLs for AI Gateway task creation and data retrieval ([1990670](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1990670f368788d2dc9f8a1697b4f5d41e88463d))
* Remove unused variables in add_task_to_ai_gateway_queue function ([f50e1fb](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f50e1fbea70fb1ea4d9ff7819995da4809a65218))
* Update AI gateway task creation URL ([f862ea7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f862ea73dff729bb294cc794e1d3877f552ae229))
* Update package installation command in init.sh ([77c1a87](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/77c1a870fd6fbdcae0f43f2669a2f0e28469630b))

## [1.0.9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.8...1.0.9) (2024-02-23)


### Bug Fixes

* added try cath blocks in endpoints ([4f10dcf](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4f10dcfaeffd69bbe154c07591c191fe7e5a9b5d))
* Disable insecure request warning in media_property_retrieval.py ([b17abc1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b17abc15825be415609ef64708e0926e0b970c10))

## [1.0.8](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.7...1.0.8) (2024-02-22)


### Bug Fixes

* Add logic to retrieve similar images and person names ([a210135](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a2101356abb9b424ec8886e5b29f369ac97702a1))

## [1.0.7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.6...1.0.7) (2024-02-22)


### Bug Fixes

* Add media properties retrieval router and update default values for environment and priority ([003a2ac](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/003a2acb28dbac20ec121f8d25276c7f42ab2491))
* Add new API endpoints for retrieving and updating celebrity names*** ([a433861](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a433861efd5232f06518d84c2849e0b161f20856))
* Fix return value in get_person_name function ([b46211c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b46211c8285c4e8a579659ea4778d52077bf2820))
* made env optional in the input payload ([39ef37a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/39ef37afc2f461a041ad4d06807ca8bcd3d97fdb))
* Update API endpoints for person name retrieval ([de91fa3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/de91fa359b56ad572706e1a19b07422a8f91e831))
* Update variable names in media_property_retrieval.py ([347db80](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/347db80759e1c44d352b3b3799b7bc60279fa5d6))

## [1.0.6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.5...1.0.6) (2024-02-21)


### Bug Fixes

* Add environment check for reuploading files ([8c77d15](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/8c77d1528f10bb231b2d2fdc6214625504553087))

## [1.0.5](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.4...1.0.5) (2024-02-21)


### Bug Fixes

* Add DEV_PUBLIC_HEADER and CCE_PUBLIC_HEADER to config.py and update store_data function in receive_router.py ([273e6a9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/273e6a9c243b667d740e5ba13ee00e8193d12bfe))
* Add media type based on file type ([deb8078](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/deb80786b3dbeac301b8f445e9d10d84190d7278))
* Refactor code to store service name and data in payload*** ([262c440](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/262c440fafe8f48943b6c7e2ebb317f2f7ec056b))
* Refactor logging and remove print statements ([32f5496](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/32f549697dcb7a8e2edcbd0f6987aeef0fd4e754))

## [1.0.4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.3...1.0.4) (2024-02-20)


### Bug Fixes

* fixed issues in submit_zip endpoint ([cc55f1c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cc55f1ca1b319579c5b467d23d07deb7ca382cb4))

## [1.0.3](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.2...1.0.3) (2024-02-20)


### Bug Fixes

* Add check for 'env' field in message_data ([b4efd77](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/b4efd77cbc13cbb206e873ebf0677d42b961f699))
* Update DEFAULT_SYSTEM_ID environment variable ([154bf76](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/154bf76f9ce5d0d2511e372b47e4a66fa748923a))

## [1.0.2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.1...1.0.2) (2024-02-20)


### Bug Fixes

* Remove redundant logging statement and add logging for message received ([f1acc3d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/f1acc3d9b62e804a24d07bb1ba1ec33946a12bf7))

## [1.0.1](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/compare/1.0.0...1.0.1) (2024-02-20)


### Bug Fixes

* Add logging for default system ID ([58f5e50](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/58f5e501943b0e9c6fafb744a1571e51078af63a))

# 1.0.0 (2024-02-20)


### Bug Fixes

* Add caseIds and entityIds parameters to upload_file function ([51edbce](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/51edbcefe733dc5168f3b44bc51c56da8c703b42))
* Add environment filter to message receiving ([33bfb0b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/33bfb0b5d67dd5023ea7f1af448a7e97e87b0048))
* added listener for azure service bus queues ([93181e6](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/93181e67d00b36f88bf665780603c723fb7e0b13))
* added logs to listener.py and added initial commit for submit_zip ([a469f5e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/a469f5eacc2cb2e5afb62b9e3eaf915b62e209b0))
* added object detection storing ([1862679](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/1862679b6c2ef6ba811c2731938f4056e9a9f5af))
* added permissions to init.sh ([fd0916a](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/fd0916a00c44d27888a8eac37f1cba098c1abf93))
* added vector search endpoints ([ae074c7](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/ae074c75a2e9b9df8e59c0237e26f9cb660de582))
* Added zip file processing in submit router ([907f6b4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/907f6b47a440f71ba219bdaf079d94ee0302632f))
* bug fix in Add background process for main.py execution ([bf1cc99](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/bf1cc997d9eb2f65155b7a1ed62ababa0b66f374))
* initial commit ([569304b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/569304bcb316b491e283a8c4daa55a802d2cf005))
* just added ogs ([c50ef2b](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/c50ef2bf7b285a89cb6e501070112b7bd06a88df))
* logs ([cce328d](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cce328ddc9639a405dddaada9edda6adc6e0d89b))
* major update to listener, receive_router ([183511c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/183511cfca2029caaa44c62ad9662a194242a5c7))
* Refactor logging in listener.py ([1976165](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/19761658d9517771fd52fa3063066934dd5af9be))
* Refactor payload processing and add face recognition data to elastic search and vdb ([430dec9](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/430dec9d5992556edfe2636db386d507eee6dc43))
* Refactor store_data function in receive_router.py ([0f4e61c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/0f4e61c13ab9b294fffb1b93b4077d3082389174))
* Refactor submit_request function to use Azure Service Bus ([d723012](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/d723012c6f591deaec0ace41875ee2da97c9ea2c))
* small bug fix in await ([5935fe2](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/5935fe2c63898dc212acdf0174b61207c120ab5d))
* small update ([cda5b8c](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/cda5b8cb0d0fb651e5533a39798f476a76b3cb09))
* Update config imports and add task to AI Gateway queue ([356e28e](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/356e28ed9c54cea737ba835f0b0d334497b24c4d))
* Update DEFAULT_SYSTEM_ID and fix service bus test output ([bc64fd4](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/bc64fd482e1bfd503147da7c07477cff189dd5a2))
* updated the code in receive_router, added local storing part ([4c2c215](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/4c2c215d613b291c668fdffb4af2fa34813b6ca7))
* updated the receive endpoint ([192eb85](https://dev.azure.com/predictintel/data-ai/_git/ai-client-server/commit/192eb8508afedb8067d69288a81ffeff72991976))
