{"cells": [{"cell_type": "code", "execution_count": 7, "id": "3586c395", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>serviceName</th>\n", "      <th>mediaType</th>\n", "      <th>apiUrl</th>\n", "      <th>apiTag</th>\n", "      <th>apiBody</th>\n", "      <th>priority</th>\n", "      <th>status</th>\n", "      <th>env</th>\n", "      <th>entity_id</th>\n", "      <th>crawler</th>\n", "      <th>fileId</th>\n", "      <th>createdDate</th>\n", "      <th>__v</th>\n", "      <th>docServerId</th>\n", "      <th>metaData</th>\n", "      <th>processingStartDate</th>\n", "      <th>completedDate</th>\n", "      <th>_messageSentToBus</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6822fcb359a81f5ff3b40965</td>\n", "      <td>ObjectDetection</td>\n", "      <td>Image</td>\n", "      <td>http://cyber-object-detection-v2-microservice....</td>\n", "      <td>DetectObjectsInImage</td>\n", "      <td>{\"file_url\": \"https://databuzz-blob.dosashop1....</td>\n", "      <td>200</td>\n", "      <td>Error</td>\n", "      <td>cybersmart-dev-akscluster2</td>\n", "      <td>Default</td>\n", "      <td>Default</td>\n", "      <td>6822fc970db3949b80d5bbd6</td>\n", "      <td>2025-05-13T08:02:59.191Z</td>\n", "      <td>0</td>\n", "      <td>6822fcb3a8acc80a5323bf54</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-13T08:03:06.460Z</td>\n", "      <td>2025-05-13T08:05:14.816Z</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6822fcb359a81f5ff3b40987</td>\n", "      <td>OcrService</td>\n", "      <td>Image</td>\n", "      <td>http://cyber-ocr-service-v2-microservice.cyber...</td>\n", "      <td>getOcrTextFromImage</td>\n", "      <td>{\"file_url\": \"https://databuzz-blob.dosashop1....</td>\n", "      <td>200</td>\n", "      <td>Error</td>\n", "      <td>cybersmart-dev-akscluster2</td>\n", "      <td>Default</td>\n", "      <td>Default</td>\n", "      <td>6822fc970db3949b80d5bbd6</td>\n", "      <td>2025-05-13T08:02:59.812Z</td>\n", "      <td>0</td>\n", "      <td>6822fcb30db3949b80d5bbe5</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-13T08:03:00.099Z</td>\n", "      <td>2025-05-13T12:43:19.491Z</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6822fcbe59a81f5ff3b40b0d</td>\n", "      <td>ObjectDetection</td>\n", "      <td>Video</td>\n", "      <td>http://cyber-object-detection-v2-microservice....</td>\n", "      <td>GetObjectDetectionByVideo</td>\n", "      <td>{\"file_url\": \"https://s3-cybersmart-raw.s3.ama...</td>\n", "      <td>200</td>\n", "      <td>Error</td>\n", "      <td>cybersmart-dev-akscluster2</td>\n", "      <td>Default</td>\n", "      <td>Default</td>\n", "      <td>s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c</td>\n", "      <td>2025-05-13T08:03:10.723Z</td>\n", "      <td>0</td>\n", "      <td>6822fcbe6e5c6c43e63a041c</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-13T08:03:16.934Z</td>\n", "      <td>2025-05-13T08:05:24.604Z</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6822fcbf59a81f5ff3b40b3b</td>\n", "      <td>KeyFrames</td>\n", "      <td>Video</td>\n", "      <td>http://cyber-keyframe-detection-microservice.c...</td>\n", "      <td>GetKeyFramesFromVideoURL</td>\n", "      <td>{\"file_url\": \"https://s3-cybersmart-raw.s3.ama...</td>\n", "      <td>200</td>\n", "      <td>Error</td>\n", "      <td>cybersmart-dev-akscluster2</td>\n", "      <td>Default</td>\n", "      <td>Default</td>\n", "      <td>s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c</td>\n", "      <td>2025-05-13T08:03:11.910Z</td>\n", "      <td>0</td>\n", "      <td>6822fcbfa8acc80a5323bf5b</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-13T08:03:11.910Z</td>\n", "      <td>2025-05-13T08:05:12.798Z</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6822fcc059a81f5ff3b40b63</td>\n", "      <td>TranscriptionService</td>\n", "      <td>Video</td>\n", "      <td>http://cyber-audio-transcription-microservice....</td>\n", "      <td>getTranscriptionsFromVideo</td>\n", "      <td>{\"file_url\": \"https://s3-cybersmart-raw.s3.ama...</td>\n", "      <td>200</td>\n", "      <td>Error</td>\n", "      <td>cybersmart-dev-akscluster2</td>\n", "      <td>Default</td>\n", "      <td>Default</td>\n", "      <td>s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c</td>\n", "      <td>2025-05-13T08:03:12.804Z</td>\n", "      <td>0</td>\n", "      <td>6822fcc042ddaf9c45cb469e</td>\n", "      <td>NaN</td>\n", "      <td>2025-05-13T08:03:12.803Z</td>\n", "      <td>2025-05-13T08:05:16.739Z</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        _id           serviceName mediaType  \\\n", "0  6822fcb359a81f5ff3b40965       ObjectDetection     Image   \n", "1  6822fcb359a81f5ff3b40987            OcrService     Image   \n", "2  6822fcbe59a81f5ff3b40b0d       ObjectDetection     Video   \n", "3  6822fcbf59a81f5ff3b40b3b             KeyFrames     Video   \n", "4  6822fcc059a81f5ff3b40b63  TranscriptionService     Video   \n", "\n", "                                              apiUrl  \\\n", "0  http://cyber-object-detection-v2-microservice....   \n", "1  http://cyber-ocr-service-v2-microservice.cyber...   \n", "2  http://cyber-object-detection-v2-microservice....   \n", "3  http://cyber-keyframe-detection-microservice.c...   \n", "4  http://cyber-audio-transcription-microservice....   \n", "\n", "                       apiTag  \\\n", "0        DetectObjectsInImage   \n", "1         getOcrTextFromImage   \n", "2   GetObjectDetectionByVideo   \n", "3    GetKeyFramesFromVideoURL   \n", "4  getTranscriptionsFromVideo   \n", "\n", "                                             apiBody  priority status  \\\n", "0  {\"file_url\": \"https://databuzz-blob.dosashop1....       200  Error   \n", "1  {\"file_url\": \"https://databuzz-blob.dosashop1....       200  Error   \n", "2  {\"file_url\": \"https://s3-cybersmart-raw.s3.ama...       200  Error   \n", "3  {\"file_url\": \"https://s3-cybersmart-raw.s3.ama...       200  Error   \n", "4  {\"file_url\": \"https://s3-cybersmart-raw.s3.ama...       200  Error   \n", "\n", "                          env entity_id  crawler  \\\n", "0  cybersmart-dev-akscluster2   Default  Default   \n", "1  cybersmart-dev-akscluster2   De<PERSON>ult  Default   \n", "2  cybersmart-dev-akscluster2   De<PERSON>ult  Default   \n", "3  cybersmart-dev-akscluster2   De<PERSON>ult  De<PERSON>ult   \n", "4  cybersmart-dev-akscluster2   De<PERSON>ult  De<PERSON>ult   \n", "\n", "                                    fileId               createdDate  __v  \\\n", "0                 6822fc970db3949b80d5bbd6  2025-05-13T08:02:59.191Z    0   \n", "1                 6822fc970db3949b80d5bbd6  2025-05-13T08:02:59.812Z    0   \n", "2  s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c  2025-05-13T08:03:10.723Z    0   \n", "3  s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c  2025-05-13T08:03:11.910Z    0   \n", "4  s3-27a658ad-8665-44d7-aebd-0ab264b8ef3c  2025-05-13T08:03:12.804Z    0   \n", "\n", "                docServerId  metaData       processingStartDate  \\\n", "0  6822fcb3a8acc80a5323bf54       NaN  2025-05-13T08:03:06.460Z   \n", "1  6822fcb30db3949b80d5bbe5       NaN  2025-05-13T08:03:00.099Z   \n", "2  6822fcbe6e5c6c43e63a041c       NaN  2025-05-13T08:03:16.934Z   \n", "3  6822fcbfa8acc80a5323bf5b       NaN  2025-05-13T08:03:11.910Z   \n", "4  6822fcc042ddaf9c45cb469e       NaN  2025-05-13T08:03:12.803Z   \n", "\n", "              completedDate  _messageSentToBus  \n", "0  2025-05-13T08:05:14.816Z               True  \n", "1  2025-05-13T12:43:19.491Z               True  \n", "2  2025-05-13T08:05:24.604Z               True  \n", "3  2025-05-13T08:05:12.798Z               True  \n", "4  2025-05-13T08:05:16.739Z               True  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv('Consolidated_AI_Models_API_DB.taskschemas.csv')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "daae43dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='serviceName'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# draw a bar chart for number of tasks per service \n", "df.groupby('serviceName').count().plot.bar(y='_id')"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}